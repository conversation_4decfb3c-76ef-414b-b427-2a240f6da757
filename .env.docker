# Docker Environment Configuration for Web Analyzer Platform

# =============================================================================
# SERVER PORTS
# =============================================================================
API_SERVER_PORT=5000
SCRAPER_ENGINE_PORT=5001
FRONTEND_PORT=3000
ADMIN_PORTAL_PORT=3001

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MongoDB Atlas (replace with your connection string)
MONGO_URI=mongodb+srv://seckrishnapatel767:<EMAIL>/?retryWrites=true&w=majority&appName=Food-for-soul-tech
MONGO_DB_NAME=web-analyzer

# =============================================================================
# REDIS CONFIGURATION (Docker Internal)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_USERNAME=
REDIS_DB=0

# =============================================================================
# AUTHENTICATION
# =============================================================================
JWT_ACCESS_TOKEN_SECRET=docker-secret-key-change-in-production-2024
JWT_REFRESH_TOKEN_SECRET=docker-refresh-secret-key-change-in-production-2024
JWT_ACCESS_TOKEN_EXPIRATION=1h
JWT_REFRESH_TOKEN_EXPIRATION=15d

# =============================================================================
# API KEYS
# =============================================================================
GEMINI_API_KEY=your-gemini-api-key
CHAT_GPT_API_KEY=your-chatgpt-api-key

# =============================================================================
# SERVICE URLS (Docker Internal)
# =============================================================================
API_SERVER_URL=http://api-server:5000
SCRAPER_ENGINE_URL=http://scraper-engine:5001
FRONTEND_URL=http://localhost:3000
ADMIN_PORTAL_URL=http://localhost:3001

# =============================================================================
# DOCKER SPECIFIC CONFIGURATION
# =============================================================================
NODE_ENV=development
DOCKER_ENV=true

# Puppeteer Configuration for Docker
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Worker concurrency for scraper engine
WORKER_CONCURRENCY=3

# Queue settings
QUEUE_REMOVE_ON_COMPLETE=100
QUEUE_REMOVE_ON_FAIL=50
QUEUE_MAX_ATTEMPTS=3

# Browser settings
BROWSER_TIMEOUT=30000
BROWSER_HEADLESS=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# CORS settings
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
PROMETHEUS_PORT=9090
