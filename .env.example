# Example environment variables for the platform
API_SERVER_PORT=5000
SCRAPER_ENGINE_PORT=5001
FRONTEND_PORT=3000
ADMIN_PORTAL_PORT=3001

# Database
DATABASE_URL=mongodb://localhost:27017/web-analyzer
DATABASE_NAME=web-analyzer

# Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRY=24h

# API Keys
OPENAI_API_KEY=your-openai-api-key
LIGHTHOUSE_API_KEY=your-lighthouse-api-key

# Services
API_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3001
ADMIN_PORTAL_URL=http://localhost:3002

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_SECURITY_SCANNING=true

# Rate Limiting
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=100

