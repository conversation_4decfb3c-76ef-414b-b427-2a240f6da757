# Production Environment Configuration for Web Analyzer Platform

# =============================================================================
# SERVER PORTS
# =============================================================================
API_SERVER_PORT=5000
SCRAPER_ENGINE_PORT=5001
FRONTEND_PORT=3000
ADMIN_PORTAL_PORT=3001

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MongoDB Atlas (CHANGE THIS TO YOUR PRODUCTION DATABASE)
MONGO_URI=mongodb+srv://production-user:<EMAIL>/?retryWrites=true&w=majority
MONGO_DB_NAME=web-analyzer-prod

# =============================================================================
# REDIS CONFIGURATION (Production)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=CHANGE-THIS-REDIS-PASSWORD-IN-PRODUCTION
REDIS_USERNAME=
REDIS_DB=0

# =============================================================================
# AUTHENTICATION (CHANGE THESE IN PRODUCTION)
# =============================================================================
JWT_ACCESS_TOKEN_SECRET=CHANGE-THIS-SUPER-SECRET-KEY-IN-PRODUCTION-2024
JWT_REFRESH_TOKEN_SECRET=CHANGE-THIS-REFRESH-SECRET-KEY-IN-PRODUCTION-2024
JWT_ACCESS_TOKEN_EXPIRATION=15m
JWT_REFRESH_TOKEN_EXPIRATION=7d

# =============================================================================
# API KEYS (ADD YOUR PRODUCTION KEYS)
# =============================================================================
GEMINI_API_KEY=your-production-gemini-api-key
CHAT_GPT_API_KEY=your-production-chatgpt-api-key

# =============================================================================
# SERVICE URLS (Production)
# =============================================================================
API_SERVER_URL=https://api.yourdomain.com
SCRAPER_ENGINE_URL=https://scraper.yourdomain.com
FRONTEND_URL=https://yourdomain.com
ADMIN_PORTAL_URL=https://admin.yourdomain.com

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================
NODE_ENV=production
DOCKER_ENV=true

# Puppeteer Configuration for Production
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/app/logs/app.log

# =============================================================================
# PERFORMANCE TUNING (Production)
# =============================================================================
# Worker concurrency for scraper engine (adjust based on server resources)
WORKER_CONCURRENCY=5

# Queue settings
QUEUE_REMOVE_ON_COMPLETE=50
QUEUE_REMOVE_ON_FAIL=25
QUEUE_MAX_ATTEMPTS=3

# Browser settings
BROWSER_TIMEOUT=45000
BROWSER_HEADLESS=true
BROWSER_POOL_SIZE=10

# =============================================================================
# SECURITY SETTINGS (Production)
# =============================================================================
# CORS settings (restrict to your domains)
CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com
CORS_CREDENTIALS=true

# Rate limiting (stricter in production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Security headers
HELMET_ENABLED=true
TRUST_PROXY=true

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# APM Configuration
APM_ENABLED=true
APM_SERVICE_NAME=web-analyzer
APM_ENVIRONMENT=production

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Maintenance mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.
