<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a monorepo for a web analyzer platform. Please generate code that is modular, uses TypeScript, and follows best practices for monorepo development. Shared logic should go in the appropriate package under `packages/`.
