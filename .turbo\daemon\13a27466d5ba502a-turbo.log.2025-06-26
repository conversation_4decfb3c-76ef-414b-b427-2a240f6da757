2025-06-26T05:35:53.318781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server"), AnchoredSystemPathBuf("apps\\scraper-engine"), AnchoredSystemPathBuf("packages\\shared-logic"), AnchoredSystemPathBuf("packages\\config")}
2025-06-26T05:35:53.318864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }, WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }, WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }, WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:35:53.825470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-06-26T05:35:53.825494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T05:35:55.113993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\dist"), AnchoredSystemPathBuf("packages\\config")}
2025-06-26T05:35:55.114019Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:35:55.373609Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.373952Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\dist"), AnchoredSystemPathBuf("apps\\scraper-engine"), AnchoredSystemPathBuf("apps\\scraper-engine\\dist"), AnchoredSystemPathBuf("packages\\db-models")}
2025-06-26T05:35:55.373961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }, WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }, WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:35:55.373985Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.447773Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.520064Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\dist")}
2025-06-26T05:35:55.520088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T05:35:55.946167Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.946533Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\dist"), AnchoredSystemPathBuf("apps\\api-server")}
2025-06-26T05:35:55.946551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T05:35:55.946587Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:56.825461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared-logic\\dist"), AnchoredSystemPathBuf("packages\\shared-logic")}
2025-06-26T05:35:56.825483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }}))
2025-06-26T05:35:56.955003Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:57.616069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\db-models\\dist")}
2025-06-26T05:35:57.616093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }}))
2025-06-26T05:35:57.721702Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:57.721782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\db-models\\dist"), AnchoredSystemPathBuf("packages\\db-models")}
2025-06-26T05:35:57.721788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }}))
2025-06-26T05:35:57.721800Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:36:22.320711Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\db-models\\package.json")}
2025-06-26T05:36:22.320731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }}))
2025-06-26T05:36:33.128306Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\package.json")}
2025-06-26T05:36:33.128319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:38:39.242808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared-logic\\package.json")}
2025-06-26T05:38:39.243204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }}))
2025-06-26T05:38:51.725789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared-logic"), AnchoredSystemPathBuf("packages\\shared-logic\\tsconfig.json")}
2025-06-26T05:38:51.725827Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }}))
