2025-06-26T05:35:53.318781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server"), AnchoredSystemPathBuf("apps\\scraper-engine"), AnchoredSystemPathBuf("packages\\shared-logic"), AnchoredSystemPathBuf("packages\\config")}
2025-06-26T05:35:53.318864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }, WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }, WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }, WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:35:53.825470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-06-26T05:35:53.825494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T05:35:55.113993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\dist"), AnchoredSystemPathBuf("packages\\config")}
2025-06-26T05:35:55.114019Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:35:55.373609Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.373952Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\dist"), AnchoredSystemPathBuf("apps\\scraper-engine"), AnchoredSystemPathBuf("apps\\scraper-engine\\dist"), AnchoredSystemPathBuf("packages\\db-models")}
2025-06-26T05:35:55.373961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }, WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }, WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:35:55.373985Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.447773Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.520064Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\dist")}
2025-06-26T05:35:55.520088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T05:35:55.946167Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:55.946533Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\dist"), AnchoredSystemPathBuf("apps\\api-server")}
2025-06-26T05:35:55.946551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T05:35:55.946587Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:56.825461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared-logic\\dist"), AnchoredSystemPathBuf("packages\\shared-logic")}
2025-06-26T05:35:56.825483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }}))
2025-06-26T05:35:56.955003Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:57.616069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\db-models\\dist")}
2025-06-26T05:35:57.616093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }}))
2025-06-26T05:35:57.721702Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:35:57.721782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\db-models\\dist"), AnchoredSystemPathBuf("packages\\db-models")}
2025-06-26T05:35:57.721788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }}))
2025-06-26T05:35:57.721800Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:36:22.320711Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\db-models\\package.json")}
2025-06-26T05:36:22.320731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/db-models"), path: AnchoredSystemPathBuf("packages\\db-models") }}))
2025-06-26T05:36:33.128306Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\package.json")}
2025-06-26T05:36:33.128319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:38:39.242808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared-logic\\package.json")}
2025-06-26T05:38:39.243204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }}))
2025-06-26T05:38:51.725789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared-logic"), AnchoredSystemPathBuf("packages\\shared-logic\\tsconfig.json")}
2025-06-26T05:38:51.725827Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/shared-logic"), path: AnchoredSystemPathBuf("packages\\shared-logic") }}))
2025-06-26T05:57:16.959452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T05:57:16.959479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T05:57:18.557358Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T05:57:18.557377Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T05:59:17.853039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:17.853062Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:19.052126Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:19.052167Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:22.757054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:22.757078Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:24.365031Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:24.365048Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:25.457440Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:25.457462Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:28.558560Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:28.558579Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:28.861245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:28.861264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:28.877674Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:59:29.063126Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:29.063154Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:29.078636Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:59:31.153142Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:31.153153Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:35.956222Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:35.956236Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:38.352093Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:38.352112Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:38.653091Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:38.653119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:38.712344Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:59:38.954424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:38.954445Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:38.954471Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:59:47.861831Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:47.861854Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:50.265621Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:50.265644Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:56.953647Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T05:59:56.953669Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T05:59:59.352413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\index.ts")}
2025-06-26T05:59:59.352427Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:59:59.556915Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\index.ts")}
2025-06-26T05:59:59.556938Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:59:59.630818Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T05:59:59.751331Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\config\\index.ts")}
2025-06-26T05:59:59.751353Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@web-analyzer/config"), path: AnchoredSystemPathBuf("packages\\config") }}))
2025-06-26T05:59:59.820309Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:00:08.964273Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:00:08.964291Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:15.653221Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine")}
2025-06-26T06:00:15.653244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:37.061866Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:00:37.061888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:38.255855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:00:38.255871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:39.856051Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:00:39.856092Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:40.158984Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:00:40.159018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:40.199391Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:00:40.451431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:00:40.451453Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:00:40.451485Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:00:59.466599Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:00:59.466738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:08.856458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:08.856494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:10.557499Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:10.557523Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:10.751138Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:10.751165Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:10.834784Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:01:10.853063Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:10.853087Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:10.853115Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:01:12.355185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:12.355196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:16.054660Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:16.054690Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:16.254923Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:16.254941Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:16.254969Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:01:16.463937Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:16.463957Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:16.526983Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:01:16.751669Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server\\src\\index.ts")}
2025-06-26T06:01:16.751752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:01:16.757031Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:01:48.355288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:48.355333Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:50.555379Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:50.555407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:52.759519Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:52.759578Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:55.363168Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:55.363196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:57.466538Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:57.466561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:59.463891Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:59.463988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:59.660798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:01:59.660824Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:01:59.715049Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:02:07.456771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:07.456801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:46.559339Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:46.559367Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:46.760742Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:46.760754Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:46.790192Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:02:47.055285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:47.055311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:47.124568Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:02:47.253933Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:47.253956Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:47.301761Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:02:47.855671Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:47.855692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:47.864007Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:02:48.053389Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:02:48.053402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:02:48.114403Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-26T06:03:42.563051Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:03:42.563072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:03:54.659918Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\src\\index.ts")}
2025-06-26T06:03:54.659957Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:04:06.651742Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine")}
2025-06-26T06:04:06.651764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:04:10.462907Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\package.json")}
2025-06-26T06:04:10.462929Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:04:22.751852Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine\\package.json")}
2025-06-26T06:04:22.751874Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:26:30.013785Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\scraper-engine"), AnchoredSystemPathBuf("apps\\scraper-engine\\Dockerfile")}
2025-06-26T06:26:30.013849Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("scraper-engine"), path: AnchoredSystemPathBuf("apps\\scraper-engine") }}))
2025-06-26T06:26:42.905657Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api-server"), AnchoredSystemPathBuf("apps\\api-server\\Dockerfile")}
2025-06-26T06:26:42.905686Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api-server"), path: AnchoredSystemPathBuf("apps\\api-server") }}))
2025-06-26T06:27:08.014873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker-compose.yml")}
2025-06-26T06:27:08.014889Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:27:08.115036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:27:08.115049Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:27:28.214258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker-compose.prod.yml")}
2025-06-26T06:27:28.214281Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:27:28.310613Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:27:28.310628Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:27:55.312118Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:27:55.312141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:28:16.610818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:28:16.610855Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:28:39.805239Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("redis.conf")}
2025-06-26T06:28:39.805254Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:28:39.917050Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("redis.conf")}
2025-06-26T06:28:39.917069Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:28:40.012275Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:28:40.012370Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:29:04.212398Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("nginx.conf"), AnchoredSystemPathBuf("")}
2025-06-26T06:29:04.212425Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:29:34.914892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker-compose.dev.yml")}
2025-06-26T06:29:34.914918Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:29:35.004666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:29:35.004692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:29:59.814898Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("Makefile")}
2025-06-26T06:29:59.814920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:29:59.916285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("Makefile"), AnchoredSystemPathBuf("")}
2025-06-26T06:29:59.916312Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:30:13.904821Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".dockerignore")}
2025-06-26T06:30:13.904841Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:30:14.019330Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:30:14.019344Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:30:34.220086Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("JOB_QUEUE_INTEGRATION.md")}
2025-06-26T06:30:34.220159Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:31:20.312965Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("DOCKER_SETUP.md")}
2025-06-26T06:31:20.312989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:31:20.409370Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:31:20.409384Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:31:40.810974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-06-26T06:31:40.811006Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-06-26T06:31:52.610506Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts"), AnchoredSystemPathBuf("scripts\\test-docker.sh"), AnchoredSystemPathBuf("")}
2025-06-26T06:31:52.610529Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-26T06:31:52.719398Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts")}
2025-06-26T06:31:52.719418Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
