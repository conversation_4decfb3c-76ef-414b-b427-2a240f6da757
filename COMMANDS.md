# Web Analyzer Platform - Command Reference

## 🚀 Quick Start Commands

### One-Command Setup (First Time)
```bash
npm run setup
```
**What it does:**
- Checks prerequisites (Docker, Node.js)
- Configures environment variables
- Builds and starts all services with Docker
- Runs integration tests
- Opens the platform in your browser

### Quick Start (Daily Use)
```bash
npm start
```
**What it does:**
- Starts all services if not running
- Performs quick health checks
- Shows service URLs and status

### Windows Users
```batch
start.bat
```
Double-click the `start.bat` file or run it from command prompt.

## 🛑 Stop Commands

### Stop All Services
```bash
npm stop
```

### Restart Services
```bash
npm restart
```

## 🐳 Docker Commands

### Basic Docker Operations
```bash
npm run docker:up      # Start services
npm run docker:down    # Stop services
npm run docker:build   # Build and start services
npm run docker:logs    # View service logs
npm run docker:clean   # Clean up containers and images
```

### Docker Testing
```bash
npm run docker:test    # Run integration tests
```

### Advanced Docker Commands
```bash
# Development environment
docker-compose -f docker-compose.dev.yml up -d

# Production environment  
docker-compose -f docker-compose.prod.yml up -d

# View specific service logs
docker-compose -f docker-compose.dev.yml logs api-server
docker-compose -f docker-compose.dev.yml logs scraper-engine
docker-compose -f docker-compose.dev.yml logs redis

# Scale services (production)
docker-compose -f docker-compose.prod.yml up -d --scale api-server=3
docker-compose -f docker-compose.prod.yml up -d --scale scraper-engine=5
```

## 🔧 Development Commands

### Traditional Development (Non-Docker)
```bash
npm run dev            # Start all services in development mode
npm run build          # Build all packages
npm run lint           # Run linting
npm run test           # Run tests
npm run clean          # Clean build artifacts
```

### Package-Specific Commands
```bash
# Build specific packages
pnpm --filter @web-analyzer/config build
pnpm --filter @web-analyzer/db-models build
pnpm --filter @web-analyzer/shared-logic build

# Run specific services
pnpm --filter api-server dev
pnpm --filter scraper-engine dev
```

## 📊 Monitoring Commands

### View Logs
```bash
npm run docker:logs                    # All services
docker logs web-analyzer-api-server-dev    # API server only
docker logs web-analyzer-scraper-engine-dev # Scraper engine only
docker logs web-analyzer-redis-dev         # Redis only
```

### Container Status
```bash
docker ps                              # Running containers
docker-compose -f docker-compose.dev.yml ps # Service status
docker stats                           # Resource usage
```

### Health Checks
```bash
# Manual health checks
curl http://localhost:5000/health      # API server
curl http://localhost:5001/health      # Scraper engine
docker exec web-analyzer-redis-dev redis-cli ping # Redis

# Automated health check
npm run docker:test
```

## 🧪 Testing Commands

### Integration Tests
```bash
npm run docker:test                    # Full integration test
node scripts/test-docker.js            # Direct Node.js test
scripts/test-docker.sh                 # Linux/Mac shell script
scripts/test-docker.bat                # Windows batch script
```

### Manual API Testing
```bash
# Create a user
curl -X POST http://localhost:5000/api/users \
  -H "Content-Type: application/json" \
  -d '{"name": "Test User", "email": "<EMAIL>"}'

# Create a scrape job
curl -X POST http://localhost:5000/api/queue/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "userId": "USER_ID_HERE",
    "options": {"screenshot": true}
  }'

# Check job status
curl http://localhost:5000/api/queue/job/JOB_ID_HERE

# Get queue statistics
curl http://localhost:5000/api/queue/stats
```

## 🧹 Cleanup Commands

### Docker Cleanup
```bash
npm run docker:clean                   # Clean project containers
docker system prune -f                 # Clean all Docker resources
docker volume prune -f                 # Clean unused volumes
docker image prune -f                  # Clean unused images
```

### Full Reset
```bash
npm run docker:clean
npm run setup                          # Fresh setup
```

## 🔧 Utility Commands

### Database Operations
```bash
# Redis operations
docker exec web-analyzer-redis-dev redis-cli
docker exec web-analyzer-redis-dev redis-cli FLUSHALL
docker exec web-analyzer-redis-dev redis-cli INFO

# MongoDB operations (if using local MongoDB)
docker exec -it mongodb-container mongo
```

### Shell Access
```bash
# Access container shells
docker exec -it web-analyzer-api-server-dev sh
docker exec -it web-analyzer-scraper-engine-dev sh
docker exec -it web-analyzer-redis-dev sh
```

### File Operations
```bash
# Copy files to/from containers
docker cp file.txt web-analyzer-api-server-dev:/app/
docker cp web-analyzer-api-server-dev:/app/logs ./logs
```

## 🎯 Make Commands (Alternative)

If you have `make` installed:

```bash
make dev              # Start development environment
make prod             # Start production environment
make build            # Build all images
make clean            # Clean up resources
make logs             # View logs
make health           # Check service health
make test             # Run tests
make tools            # Start development tools
```

## 🌐 Service URLs

### Core Services
- **API Server**: http://localhost:5000
- **Scraper Engine**: http://localhost:5001

### Management Tools
- **Redis Commander**: http://localhost:8081
- **Mongo Express**: http://localhost:8082 (with tools profile)
- **Mailhog**: http://localhost:8025 (with tools profile)

### API Endpoints
- **Health Check**: http://localhost:5000/health
- **Users API**: http://localhost:5000/api/users
- **Queue API**: http://localhost:5000/api/queue/
- **Queue Stats**: http://localhost:5000/api/queue/stats

## 🚨 Troubleshooting Commands

### Common Issues
```bash
# Port conflicts
netstat -an | findstr :5000           # Windows
lsof -i :5000                         # Linux/Mac

# Docker issues
docker system df                      # Check disk usage
docker system events                  # Monitor Docker events
docker info                          # Docker system info

# Service issues
npm run docker:logs                   # Check logs
docker-compose -f docker-compose.dev.yml ps # Check status
```

### Reset Everything
```bash
# Nuclear option - reset everything
docker-compose -f docker-compose.dev.yml down -v --remove-orphans
docker system prune -a -f --volumes
npm run setup
```

## 📝 Environment Commands

### Environment Files
```bash
# Copy environment templates
cp .env.docker .env                   # Use Docker config
cp .env.production .env               # Use production config

# Edit environment
nano .env.docker                      # Linux/Mac
notepad .env.docker                   # Windows
```

### Environment Validation
```bash
# Check environment variables
docker-compose -f docker-compose.dev.yml config
```

## 🔄 Update Commands

### Update Dependencies
```bash
pnpm update                           # Update all dependencies
pnpm --filter api-server update       # Update specific package
```

### Rebuild After Changes
```bash
npm run docker:build                 # Rebuild and restart
docker-compose -f docker-compose.dev.yml up -d --build
```

---

**Quick Reference Card:**
- **Start**: `npm start`
- **Stop**: `npm stop`
- **Setup**: `npm run setup`
- **Logs**: `npm run docker:logs`
- **Test**: `npm run docker:test`
- **Clean**: `npm run docker:clean`
