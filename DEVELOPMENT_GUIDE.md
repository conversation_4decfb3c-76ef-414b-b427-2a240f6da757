# Development Guide

## 🚀 Quick Start Options

### Option 1: Docker (Recommended)
```bash
npm start
```
This starts everything with Dock<PERSON> - no setup needed!

### Option 2: Local Development
```bash
npm run dev
```
This starts Redis with Docker and runs API/Scraper locally with hot reload.

### Option 3: Manual Setup
```bash
# Start Redis
npm run redis:start

# Start services individually
pnpm --filter api-server dev
pnpm --filter scraper-engine dev
```

## 🔧 Development Commands

### Primary Commands
- `npm run dev` - Start development environment (Redis + local services)
- `npm start` - Start with Docker (full containerization)
- `npm stop` - Stop Docker services
- `npm run setup` - Full guided setup

### Individual Service Commands
- `pnpm --filter api-server dev` - Start API server only
- `pnpm --filter scraper-engine dev` - Start scraper engine only
- `npm run redis:start` - Start Redis container only

### Build Commands
- `npm run build` - Build all packages
- `pnpm --filter @web-analyzer/shared-logic build` - Build shared logic
- `pnpm --filter @web-analyzer/db-models build` - Build database models

## 🐛 Troubleshooting

### Issue: TypeScript Compilation Errors

**Symptoms:**
```
[ERROR] ⨯ Unable to compile TypeScript:
src/routes/queue.ts(35,14): error TS18046: 'scrapeJob._id' is of type 'unknown'.
```

**Solution:**
1. Build shared packages first:
   ```bash
   pnpm --filter @web-analyzer/config build
   pnpm --filter @web-analyzer/db-models build
   pnpm --filter @web-analyzer/shared-logic build
   ```

2. Restart the development server:
   ```bash
   pnpm --filter api-server dev
   ```

### Issue: Redis Connection Errors

**Symptoms:**
```
Error: connect ECONNREFUSED 127.0.0.1:6379
```

**Solutions:**

1. **Start Redis with Docker:**
   ```bash
   npm run redis:start
   ```

2. **Check if Redis is running:**
   ```bash
   docker ps | grep redis
   ```

3. **Restart Redis container:**
   ```bash
   docker restart web-analyzer-redis-dev
   ```

4. **Use Docker for everything:**
   ```bash
   npm start
   ```

### Issue: Port Already in Use

**Symptoms:**
```
Error: listen EADDRINUSE: address already in use :::5000
```

**Solutions:**

1. **Find what's using the port:**
   ```bash
   # Windows
   netstat -ano | findstr :5000
   
   # Linux/Mac
   lsof -i :5000
   ```

2. **Kill the process:**
   ```bash
   # Windows (replace PID with actual process ID)
   taskkill /PID <PID> /F
   
   # Linux/Mac
   kill -9 <PID>
   ```

3. **Use different ports:**
   Update `.env` file:
   ```env
   API_SERVER_PORT=5002
   SCRAPER_ENGINE_PORT=5003
   ```

### Issue: MongoDB Connection Errors

**Symptoms:**
```
MongooseServerSelectionError: Could not connect to any servers
```

**Solutions:**

1. **Check MongoDB URI in .env:**
   ```env
   MONGO_URI=mongodb+srv://username:<EMAIL>/database
   ```

2. **Test connection:**
   ```bash
   # Use MongoDB Compass or mongo shell
   mongo "mongodb+srv://your-connection-string"
   ```

3. **Check network connectivity:**
   - Ensure you're connected to the internet
   - Check if your IP is whitelisted in MongoDB Atlas

### Issue: Docker Issues

**Symptoms:**
```
Cannot connect to the Docker daemon
```

**Solutions:**

1. **Start Docker Desktop:**
   - Windows/Mac: Start Docker Desktop application
   - Linux: `sudo systemctl start docker`

2. **Check Docker status:**
   ```bash
   docker info
   ```

3. **Restart Docker:**
   - Windows/Mac: Restart Docker Desktop
   - Linux: `sudo systemctl restart docker`

## 🔄 Development Workflow

### Daily Development
1. Start development environment:
   ```bash
   npm run dev
   ```

2. Make code changes (hot reload enabled)

3. Test your changes:
   ```bash
   curl http://localhost:5000/health
   curl http://localhost:5001/health
   ```

4. Stop when done:
   ```bash
   Ctrl+C
   ```

### Working with Packages

1. **Modify shared packages:**
   ```bash
   # After changes to packages/shared-logic
   pnpm --filter @web-analyzer/shared-logic build
   
   # Restart services to pick up changes
   pnpm --filter api-server dev
   ```

2. **Add new dependencies:**
   ```bash
   # Add to specific package
   pnpm --filter api-server add express
   
   # Add to workspace root
   pnpm add -w typescript
   ```

### Testing Changes

1. **API Testing:**
   ```bash
   # Create user
   curl -X POST http://localhost:5000/api/users \
     -H "Content-Type: application/json" \
     -d '{"name": "Test", "email": "<EMAIL>"}'
   
   # Create job
   curl -X POST http://localhost:5000/api/queue/scrape \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com", "userId": "USER_ID"}'
   ```

2. **Integration Testing:**
   ```bash
   npm run docker:test
   ```

## 🏗️ Architecture Overview

```
Development Setup:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Server    │    │  Redis (Docker) │    │ Scraper Engine  │
│   (Local Dev)   │───▶│   Port: 6379    │───▶│   (Local Dev)   │
│   Port: 5000    │    └─────────────────┘    │   Port: 5001    │
└─────────────────┘                           └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MongoDB Atlas                               │
│                  (Cloud Database)                              │
└─────────────────────────────────────────────────────────────────┘
```

## 📁 Project Structure

```
apps/
├── api-server/          # REST API server
│   ├── src/
│   │   ├── routes/      # API routes
│   │   ├── middleware/  # Express middleware
│   │   └── index.ts     # Server entry point
│   └── package.json
└── scraper-engine/      # Web scraping service
    ├── src/
    │   ├── workers/     # Job workers
    │   └── index.ts     # Worker entry point
    └── package.json

packages/
├── shared-logic/        # Queue and shared utilities
├── db-models/          # MongoDB models
└── config/             # Configuration
```

## 🔧 Environment Configuration

### Development (.env)
```env
# Database
MONGO_URI=mongodb+srv://...
MONGO_DB_NAME=web-analyzer-dev

# Services
API_SERVER_PORT=5000
SCRAPER_ENGINE_PORT=5001

# Redis (local Docker)
REDIS_URL=redis://localhost:6379

# Auth
JWT_ACCESS_TOKEN_SECRET=dev-secret
JWT_REFRESH_TOKEN_SECRET=dev-refresh-secret
```

### Docker (.env.docker)
```env
# Same as above but with Docker networking
REDIS_URL=redis://redis:6379
```

## 🚨 Common Gotchas

1. **Build shared packages first** before starting services
2. **Redis must be running** for queue functionality
3. **MongoDB Atlas IP whitelist** must include your IP
4. **Port conflicts** - check what's using ports 5000, 5001, 6379
5. **Docker memory** - ensure Docker has enough memory (4GB+)

## 🎯 Best Practices

1. **Use npm run dev** for daily development
2. **Use npm start** for testing full Docker setup
3. **Build shared packages** after making changes
4. **Test with curl** before writing frontend code
5. **Check logs** when things don't work: `npm run docker:logs`

## 🆘 Getting Help

If you're still having issues:

1. **Check logs:**
   ```bash
   npm run docker:logs
   ```

2. **Clean and restart:**
   ```bash
   npm run docker:clean
   npm run setup
   ```

3. **Use Docker for everything:**
   ```bash
   npm start
   ```

4. **Check service status:**
   ```bash
   docker ps
   curl http://localhost:5000/health
   curl http://localhost:5001/health
   ```
