# Docker Setup Documentation

## Overview

This document provides comprehensive instructions for setting up and running the Web Analyzer Platform using Docker. The platform includes containerized services for the API server, scraper engine, Redis queue, and supporting tools.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx LB      │    │   API Server    │    │ Scraper Engine  │
│   Port: 80/443  │───▶│   Port: 5000    │    │   Port: 5001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────────────────────────────┐
                       │              Redis Queue               │
                       │              Port: 6379                │
                       └─────────────────────────────────────────┘
                                         │
                                         ▼
                       ┌─────────────────────────────────────────┐
                       │            MongoDB Atlas               │
                       │         (External Service)             │
                       └─────────────────────────────────────────┘
```

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Make (optional, for convenience commands)
- 4GB+ RAM available for containers
- 10GB+ disk space

## Quick Start

### Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd AI-web-scrapping

# Copy environment file
cp .env.docker .env

# Start development environment
make dev

# Or without Make
docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d
```

### Production Environment

```bash
# Copy production environment file
cp .env.production .env

# Update production secrets
nano .env

# Start production environment
make prod

# Or without Make
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d
```

## Environment Files

### Development (.env.docker)
- Pre-configured for local development
- Uses internal Docker networking
- Includes debugging configurations
- Redis without password

### Production (.env.production)
- Requires manual configuration
- Security-focused settings
- SSL/TLS configurations
- Redis with authentication

## Docker Compose Files

### docker-compose.yml (Standard)
- Basic setup with all services
- Suitable for testing and demos
- Single instance of each service

### docker-compose.dev.yml (Development)
- Hot reload enabled
- Debug ports exposed (9229, 9230)
- Volume mounts for live code changes
- Development tools included

### docker-compose.prod.yml (Production)
- Multi-stage builds for optimization
- Multiple replicas for scaling
- Health checks and restart policies
- Nginx load balancer included

## Services

### API Server
- **Image**: Custom Node.js Alpine
- **Port**: 5000
- **Health Check**: `/health` endpoint
- **Scaling**: Horizontal (multiple replicas)

### Scraper Engine
- **Image**: Custom Node.js Alpine + Chromium
- **Port**: 5001
- **Special Requirements**: 
  - Shared memory (2GB)
  - Security options for Puppeteer
- **Scaling**: Horizontal (multiple workers)

### Redis
- **Image**: Redis 7 Alpine
- **Port**: 6379
- **Persistence**: Volume mounted
- **Configuration**: Custom redis.conf

### Nginx (Production)
- **Image**: Nginx Alpine
- **Ports**: 80, 443
- **Purpose**: Load balancing and SSL termination
- **Configuration**: Custom nginx.conf

## Make Commands

### Development
```bash
make dev          # Start development environment
make dev-build    # Build and start development
make dev-down     # Stop development environment
make dev-logs     # View development logs
```

### Production
```bash
make prod         # Start production environment
make prod-build   # Build and start production
make prod-down    # Stop production environment
make prod-logs    # View production logs
```

### Utilities
```bash
make build        # Build all images
make clean        # Clean up resources
make logs         # View all logs
make shell-api    # Shell into API container
make shell-scraper # Shell into scraper container
make redis-cli    # Connect to Redis CLI
make health       # Check service health
make stats        # Show container stats
```

### Tools
```bash
make tools        # Start development tools
# - Redis Commander: http://localhost:8081
# - Mongo Express: http://localhost:8082
# - Mailhog: http://localhost:8025
```

## Configuration

### Environment Variables

#### Required
- `MONGO_URI`: MongoDB connection string
- `MONGO_DB_NAME`: Database name
- `JWT_ACCESS_TOKEN_SECRET`: JWT secret
- `JWT_REFRESH_TOKEN_SECRET`: JWT refresh secret

#### Optional
- `REDIS_PASSWORD`: Redis authentication
- `WORKER_CONCURRENCY`: Scraper worker count
- `BROWSER_TIMEOUT`: Puppeteer timeout
- `LOG_LEVEL`: Logging level

### Volume Mounts

#### Development
- Source code mounted for hot reload
- Node modules cached in anonymous volumes
- Redis data persisted

#### Production
- Only data volumes mounted
- Application code baked into images
- Optimized for performance

## Networking

### Internal Network
- All services communicate via Docker network
- Service discovery by container name
- No external dependencies for inter-service communication

### External Access
- API Server: `http://localhost:5000`
- Scraper Engine: `http://localhost:5001`
- Redis Commander: `http://localhost:8081`
- Nginx (Production): `http://localhost:80`

## Security

### Development
- Basic security for local development
- Debug ports exposed
- Simplified authentication

### Production
- Non-root users in containers
- Security headers via Nginx
- Rate limiting configured
- SSL/TLS ready
- Secrets management via environment

## Monitoring

### Health Checks
- All services have health endpoints
- Docker health checks configured
- Automatic restart on failure

### Logging
- Centralized logging via Docker
- JSON format for structured logs
- Log rotation configured

### Metrics
- Container stats via `make stats`
- Service health via `make health`
- Redis monitoring via Redis Commander

## Scaling

### Horizontal Scaling
```bash
# Scale API servers
docker-compose -f docker-compose.prod.yml up -d --scale api-server=3

# Scale scraper engines
docker-compose -f docker-compose.prod.yml up -d --scale scraper-engine=5
```

### Resource Limits
- Memory limits configured per service
- CPU reservations set
- Shared memory for Puppeteer

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker-compose logs service-name

# Check container status
docker ps -a

# Rebuild if needed
make rebuild
```

#### Out of Memory
```bash
# Check memory usage
docker stats

# Increase Docker memory limit
# Reduce worker concurrency
```

#### Network Issues
```bash
# Check network
docker network ls
docker network inspect web-analyzer-network

# Restart networking
docker-compose down && docker-compose up -d
```

#### Redis Connection Failed
```bash
# Check Redis status
make redis-cli

# Verify Redis logs
docker-compose logs redis
```

### Debug Mode

#### Enable Debug Logging
```bash
# Set environment variable
LOG_LEVEL=debug

# Restart services
make restart
```

#### Access Debug Ports
- API Server: `localhost:9229`
- Scraper Engine: `localhost:9230`

## Backup and Recovery

### Redis Backup
```bash
# Create backup
make db-backup

# Manual backup
docker exec web-analyzer-redis-dev redis-cli BGSAVE
```

### Volume Backup
```bash
# Backup volumes
docker run --rm -v web-analyzer_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz /data
```

## Performance Optimization

### Production Optimizations
- Multi-stage Docker builds
- Alpine Linux base images
- Optimized layer caching
- Resource limits and reservations

### Development Optimizations
- Volume caching for node_modules
- Hot reload for faster development
- Shared packages between services

## Deployment

### Local Deployment
```bash
make prod
```

### Cloud Deployment
1. Update `.env.production` with cloud-specific settings
2. Configure external MongoDB and Redis if needed
3. Set up SSL certificates
4. Deploy using cloud-specific Docker orchestration

### CI/CD Integration
- Docker images can be built in CI/CD pipelines
- Environment-specific configurations
- Automated testing in containers

---

**Status**: ✅ Production Ready
**Last Updated**: June 26, 2025
**Docker Version**: 20.10+
**Compose Version**: 2.0+
