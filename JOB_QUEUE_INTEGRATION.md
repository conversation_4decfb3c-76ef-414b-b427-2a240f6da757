# Job Queue Integration Documentation

## Overview

This document describes the complete job queue integration between the API server and scraper engine using BullMQ and Redis. The system enables asynchronous web scraping with real-time job tracking and status updates.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Server    │    │      Redis      │    │ Scraper Engine  │
│   (Producer)    │───▶│   Job Queue     │───▶│   (Consumer)    │
│   Port: 5000    │    │   Port: 6379    │    │   Port: 5001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MongoDB Atlas                               │
│              (Job Status & Results Storage)                    │
└─────────────────────────────────────────────────────────────────┘
```

## Components

### 1. Shared Logic Package (`packages/shared-logic/`)

#### Queue Types (`queues/types.ts`)
- **ScrapeJobData**: Input data for scrape jobs
- **ScrapeJobResult**: Output data from completed jobs
- **JobProgress**: Progress tracking interface
- **QueueNames**: Enum for queue identifiers
- **JobStatus**: Job state enumeration

#### Queue Connection (`queues/connection.ts`)
- **redisConnection**: Redis configuration for BullMQ
- **scrapeJobsQueue**: Main job queue instance
- **scrapeJobsQueueEvents**: Queue event monitoring
- **Helper Functions**:
  - `addScrapeJob()`: Add jobs to queue
  - `getJobStatus()`: Get job details
  - `getQueueStats()`: Get queue statistics
  - `closeQueues()`: Graceful shutdown

#### Job Status Service (`services/jobStatusService.ts`)
- **JobStatusService**: Singleton service for job management
- **Event Listeners**: Real-time queue event handling
- **Database Integration**: Job status updates in MongoDB
- **Statistics**: Job analytics and user queries

### 2. API Server Integration (`apps/api-server/`)

#### Queue Routes (`src/routes/queue.ts`)
- `POST /api/queue/scrape` - Create new scrape job
- `GET /api/queue/job/:jobId` - Get job status and results
- `GET /api/queue/stats` - Get queue statistics
- `GET /api/queue/jobs` - List jobs with pagination and filtering

#### Database Model Updates
- **ScrapeJob Model**: Added `queueJobId` field for queue tracking
- **Job Lifecycle**: Database records linked to queue jobs

### 3. Scraper Engine Integration (`apps/scraper-engine/`)

#### Worker Implementation (`src/workers/scrapeWorker.ts`)
- **Puppeteer Integration**: Full browser automation
- **Data Extraction**:
  - Page title and content
  - Links and images
  - Custom selector data
  - Screenshots (optional)
- **Progress Tracking**: Real-time progress updates
- **Error Handling**: Comprehensive error management
- **Concurrency**: Configurable concurrent job processing

## API Endpoints

### API Server Endpoints

#### Create Scrape Job
```http
POST /api/queue/scrape
Content-Type: application/json

{
  "url": "https://example.com",
  "userId": "user_id_here",
  "options": {
    "waitForSelector": ".content",
    "timeout": 30000,
    "screenshot": true,
    "fullPage": false,
    "extractText": true,
    "extractLinks": true,
    "extractImages": true,
    "customSelectors": {
      "title": "h1",
      "description": ".description"
    }
  }
}
```

#### Get Job Status
```http
GET /api/queue/job/{jobId}
```

#### Get Queue Statistics
```http
GET /api/queue/stats
```

#### List Jobs
```http
GET /api/queue/jobs?status=completed&limit=20&offset=0
```

### Scraper Engine Endpoints

#### Queue Statistics
```http
GET /queue/stats
```

## Job Lifecycle

1. **Job Creation**: API server receives scrape request
2. **Database Record**: Creates ScrapeJob document with "pending" status
3. **Queue Addition**: Adds job to Redis queue with BullMQ
4. **Worker Processing**: Scraper engine worker picks up job
5. **Status Updates**: Real-time status updates via queue events
6. **Data Extraction**: Puppeteer performs web scraping
7. **Result Storage**: Results saved to database
8. **Completion**: Job marked as "completed" or "failed"

## Job Options

### Scraping Options
- `waitForSelector`: CSS selector to wait for before scraping
- `timeout`: Maximum time to wait for page load (ms)
- `screenshot`: Capture page screenshot (boolean)
- `fullPage`: Full page screenshot vs viewport only
- `extractText`: Extract page text content
- `extractLinks`: Extract all page links
- `extractImages`: Extract all image URLs
- `customSelectors`: Object mapping names to CSS selectors

### Queue Options
- `delay`: Delay before job execution (ms)
- `attempts`: Number of retry attempts on failure
- `backoff`: Retry strategy configuration
- `removeOnComplete`: Number of completed jobs to keep
- `removeOnFail`: Number of failed jobs to keep

## Environment Configuration

### Required Environment Variables
```env
# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_USERNAME=
REDIS_DB=0

# MongoDB Configuration
MONGO_URI=mongodb+srv://...
MONGO_DB_NAME=web-analyzer

# Server Ports
API_SERVER_PORT=5000
SCRAPER_ENGINE_PORT=5001
```

## Setup Instructions

### Option 1: Docker Setup (Recommended)
```bash
# Development environment
make dev

# Or manually
docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d

# Production environment
make prod

# Or manually
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d
```

### Option 2: Manual Setup
```bash
# 1. Install Redis
# Windows (Chocolatey)
choco install redis-64

# macOS (Homebrew)
brew install redis

# Ubuntu/Debian
sudo apt install redis-server

# Docker
docker run -d -p 6379:6379 redis:alpine

# 2. Start Services
# Start API Server
pnpm --filter api-server dev

# Start Scraper Engine
pnpm --filter scraper-engine dev
```

### 3. Test Integration
```bash
# Create a user
curl -X POST http://localhost:5000/api/users \
  -H "Content-Type: application/json" \
  -d '{"name": "Test User", "email": "<EMAIL>"}'

# Create a scrape job
curl -X POST http://localhost:5000/api/queue/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "userId": "USER_ID_FROM_ABOVE",
    "options": {"screenshot": true}
  }'

# Check job status
curl http://localhost:5000/api/queue/job/JOB_ID_FROM_ABOVE
```

## Monitoring and Debugging

### Queue Statistics
- Monitor queue health via `/api/queue/stats`
- Track job counts: waiting, active, completed, failed
- Monitor worker performance and throughput

### Logging
- Queue events logged in real-time
- Job progress updates with timestamps
- Error details for failed jobs
- Worker lifecycle events

### Database Queries
```javascript
// Get all pending jobs
db.scrapejobs.find({status: "pending"})

// Get jobs by user
db.scrapejobs.find({userId: ObjectId("...")})

// Get failed jobs with errors
db.scrapejobs.find({status: "failed", error: {$exists: true}})
```

## Production Considerations

### Scaling
- **Horizontal Scaling**: Multiple scraper engine instances
- **Queue Partitioning**: Separate queues for different job types
- **Load Balancing**: Distribute API requests across instances

### Security
- **Redis Authentication**: Use password-protected Redis
- **Rate Limiting**: Implement request rate limiting
- **Input Validation**: Validate URLs and options
- **Resource Limits**: Set memory and CPU limits for Puppeteer

### Monitoring
- **Health Checks**: Monitor service availability
- **Metrics**: Track job processing times and success rates
- **Alerts**: Set up alerts for queue backlogs and failures
- **Logging**: Centralized logging with structured data

## Troubleshooting

### Common Issues
1. **Redis Connection Failed**: Check Redis server status and connection string
2. **Jobs Stuck in Queue**: Verify worker is running and processing jobs
3. **Puppeteer Errors**: Check browser dependencies and memory limits
4. **Database Connection Issues**: Verify MongoDB Atlas connectivity

### Debug Commands
```bash
# Check Redis connection
redis-cli ping

# Monitor Redis activity
redis-cli monitor

# Check queue contents
redis-cli keys "*"

# View job details in Redis
redis-cli hgetall "bull:scrape-jobs:JOB_ID"
```

## Future Enhancements

- **Real-time WebSocket Updates**: Live job progress for frontend
- **Job Scheduling**: Cron-like job scheduling capabilities
- **Result Caching**: Cache scraping results for duplicate URLs
- **Proxy Support**: Rotate proxies for large-scale scraping
- **Content Analysis**: AI-powered content analysis and extraction
- **Webhook Notifications**: HTTP callbacks for job completion

---

**Status**: ✅ Fully Implemented and Ready for Production
**Last Updated**: June 26, 2025
**Dependencies**: Redis, MongoDB Atlas, BullMQ, Puppeteer
