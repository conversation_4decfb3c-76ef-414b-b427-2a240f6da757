# Web Analyzer Platform - Docker Management

.PHONY: help build up down logs clean dev prod test

# Default target
help:
	@echo "Web Analyzer Platform - Docker Commands"
	@echo "======================================="
	@echo ""
	@echo "Development Commands:"
	@echo "  make dev          - Start development environment"
	@echo "  make dev-build    - Build and start development environment"
	@echo "  make dev-down     - Stop development environment"
	@echo "  make dev-logs     - View development logs"
	@echo ""
	@echo "Production Commands:"
	@echo "  make prod         - Start production environment"
	@echo "  make prod-build   - Build and start production environment"
	@echo "  make prod-down    - Stop production environment"
	@echo "  make prod-logs    - View production logs"
	@echo ""
	@echo "Utility Commands:"
	@echo "  make build        - Build all Docker images"
	@echo "  make clean        - Clean up Docker resources"
	@echo "  make logs         - View all logs"
	@echo "  make shell-api    - Shell into API server container"
	@echo "  make shell-scraper - Shell into scraper engine container"
	@echo "  make redis-cli    - Connect to Redis CLI"
	@echo "  make test         - Run tests in containers"
	@echo ""
	@echo "Monitoring Commands:"
	@echo "  make tools        - Start development tools (Redis Commander, etc.)"
	@echo "  make stats        - Show container stats"
	@echo "  make health       - Check service health"

# Development Environment
dev:
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d
	@echo "Development environment started!"
	@echo "API Server: http://localhost:5000"
	@echo "Scraper Engine: http://localhost:5001"
	@echo "Redis Commander: http://localhost:8081"

dev-build:
	@echo "Building and starting development environment..."
	docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d --build
	@echo "Development environment built and started!"

dev-down:
	@echo "Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down

dev-logs:
	docker-compose -f docker-compose.dev.yml logs -f

# Production Environment
prod:
	@echo "Starting production environment..."
	docker-compose -f docker-compose.prod.yml --env-file .env.production up -d
	@echo "Production environment started!"

prod-build:
	@echo "Building and starting production environment..."
	docker-compose -f docker-compose.prod.yml --env-file .env.production up -d --build
	@echo "Production environment built and started!"

prod-down:
	@echo "Stopping production environment..."
	docker-compose -f docker-compose.prod.yml down

prod-logs:
	docker-compose -f docker-compose.prod.yml logs -f

# Standard Environment (default docker-compose.yml)
up:
	@echo "Starting standard environment..."
	docker-compose --env-file .env.docker up -d

build:
	@echo "Building all Docker images..."
	docker-compose build

down:
	@echo "Stopping all services..."
	docker-compose down

logs:
	docker-compose logs -f

# Development Tools
tools:
	@echo "Starting development tools..."
	docker-compose -f docker-compose.dev.yml --profile tools up -d
	@echo "Tools started:"
	@echo "Redis Commander: http://localhost:8081"
	@echo "Mongo Express: http://localhost:8082"
	@echo "Mailhog: http://localhost:8025"

# Shell Access
shell-api:
	docker exec -it web-analyzer-api-server-dev sh

shell-scraper:
	docker exec -it web-analyzer-scraper-engine-dev sh

redis-cli:
	docker exec -it web-analyzer-redis-dev redis-cli

# Monitoring
stats:
	docker stats

health:
	@echo "Checking service health..."
	@echo "API Server:"
	@curl -s http://localhost:5000/health | jq . || echo "API Server not responding"
	@echo ""
	@echo "Scraper Engine:"
	@curl -s http://localhost:5001/health | jq . || echo "Scraper Engine not responding"
	@echo ""
	@echo "Redis:"
	@docker exec web-analyzer-redis-dev redis-cli ping || echo "Redis not responding"

# Testing
test:
	@echo "Running tests in containers..."
	docker-compose -f docker-compose.dev.yml exec api-server pnpm test
	docker-compose -f docker-compose.dev.yml exec scraper-engine pnpm test

# Cleanup
clean:
	@echo "Cleaning up Docker resources..."
	docker-compose down -v --remove-orphans
	docker-compose -f docker-compose.dev.yml down -v --remove-orphans
	docker-compose -f docker-compose.prod.yml down -v --remove-orphans
	docker system prune -f
	@echo "Cleanup completed!"

# Database Operations
db-backup:
	@echo "Creating database backup..."
	docker exec web-analyzer-redis-dev redis-cli BGSAVE
	@echo "Redis backup created!"

db-restore:
	@echo "Restoring database..."
	@echo "Please implement database restore logic"

# Scaling (Production)
scale-api:
	docker-compose -f docker-compose.prod.yml up -d --scale api-server=3

scale-scraper:
	docker-compose -f docker-compose.prod.yml up -d --scale scraper-engine=5

# Quick Commands
restart: down up
rebuild: down build up
reset: clean dev-build
