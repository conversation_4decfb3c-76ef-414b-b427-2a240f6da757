# Web Analyzer Platform

A comprehensive web analysis platform with AI-powered insights, SEO auditing, and performance monitoring capabilities.

## Project Structure

```
web-analyzer-platform/
├── apps/                      # Independent deployable apps
│   ├── frontend/              # User-facing React/Next.js web app
│   ├── admin-portal/          # Global Super Admin UI
│   ├── scraper-engine/        # Web scraping engine (Node.js, Puppeteer)
│   └── api-server/            # Backend API (Node.js/Express/NestJS/Fastify)

├── packages/                  # Shared libraries and core logic
│   ├── ai-core/               # AI prompt handling, summarizers, GPT integrations
│   ├── audit-engine/          # Core audit logic (Lighthouse, SEO checks, etc.)
│   ├── db-models/             # Mongoose/Prisma models & shared DB logic
│   ├── scraper-core/          # Scraper utilities, rule builders, data extractors
│   ├── auth-lib/              # Shared authentication logic (JWT, 2FA, RBAC)
│   ├── report-generator/      # PDF, public link, and report generation logic
│   ├── utils/                 # Common utility functions
│   └── types/                 # Shared TypeScript types/interfaces

├── infrastructure/            # DevOps, Infrastructure, Scripts
│   ├── docker/                # Dockerfiles for each service
│   ├── k8s/                  # Kubernetes manifests
│   ├── terraform/            # Cloud infrastructure as code
│   └── scripts/              # Setup, bootstrap, and CLI utilities
```

## Getting Started

1. Install dependencies:

```bash
npm install
```

2. Run development environment:

```bash
npm run dev
```

## Scripts

- `npm run build`: Build all packages and applications
- `npm run dev`: Run all applications in development mode
- `npm run lint`: Run linting across all packages
- `npm run test`: Run tests across all packages
- `npm run clean`: Clean all build artifacts

## Development

This is a monorepo using Turborepo for build orchestration. Each package and application can be developed independently while sharing common dependencies and build configurations.
