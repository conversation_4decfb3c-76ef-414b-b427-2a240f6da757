# Web Analyzer Platform

A comprehensive web scraping and analysis platform built with Node.js, Docker, and modern web technologies. Features asynchronous job processing, real-time monitoring, and scalable architecture.

## Project Structure

```
web-analyzer-platform/
├── apps/                      # Independent deployable apps
│   ├── frontend/              # User-facing React/Next.js web app
│   ├── admin-portal/          # Global Super Admin UI
│   ├── scraper-engine/        # Web scraping engine (Node.js, Puppeteer)
│   └── api-server/            # Backend API (Node.js/Express/NestJS/Fastify)

├── packages/                  # Shared libraries and core logic
│   ├── ai-core/               # AI prompt handling, summarizers, GPT integrations
│   ├── audit-engine/          # Core audit logic (Lighthouse, SEO checks, etc.)
│   ├── db-models/             # Mongoose/Prisma models & shared DB logic
│   ├── scraper-core/          # Scraper utilities, rule builders, data extractors
│   ├── auth-lib/              # Shared authentication logic (JWT, 2FA, RBAC)
│   ├── report-generator/      # PDF, public link, and report generation logic
│   ├── utils/                 # Common utility functions
│   └── types/                 # Shared TypeScript types/interfaces

├── infrastructure/            # DevOps, Infrastructure, Scripts
│   ├── docker/                # Dockerfiles for each service
│   ├── k8s/                  # Kubernetes manifests
│   ├── terraform/            # Cloud infrastructure as code
│   └── scripts/              # Setup, bootstrap, and CLI utilities
```

## 🚀 Quick Start

### One-Command Setup
```bash
npm run setup
```
This will automatically:
- Check prerequisites (Docker, Node.js)
- Configure environment variables
- Build and start all services with Docker
- Run integration tests
- Open the platform in your browser

### Quick Start (if already set up)
```bash
npm start
```

### Windows Users
```batch
start.bat
```
Double-click the `start.bat` file in the root directory or run it from command prompt.

### Stop Services
```bash
npm stop
```

## 📋 Available Services

Once running, you can access:

- **API Server**: http://localhost:5000
- **Scraper Engine**: http://localhost:5001
- **Redis Commander**: http://localhost:8081 (Queue monitoring)

## 🛠️ Available Commands

### Quick Commands
```bash
npm start              # Start the platform (quick)
npm stop               # Stop all services
npm restart            # Restart all services
npm run setup          # Full setup with guided configuration
```

### Docker Commands
```bash
npm run docker:up      # Start services
npm run docker:down    # Stop services
npm run docker:build   # Build and start services
npm run docker:logs    # View service logs
npm run docker:test    # Run integration tests
npm run docker:clean   # Clean up containers and images
```

### Development Commands
```bash
npm run dev            # Start development servers (non-Docker)
npm run build          # Build all packages
npm run lint           # Run linting
npm run test           # Run tests
npm run clean          # Clean build artifacts
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Server    │    │      Redis      │    │ Scraper Engine  │
│   Port: 5000    │───▶│   Job Queue     │───▶│   Port: 5001    │
└─────────────────┘    │   Port: 6379    │    └─────────────────┘
         │              └─────────────────┘              │
         ▼                                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MongoDB Atlas                               │
│              (Job Status & Results Storage)                    │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 Features

### Core Services
- **API Server**: RESTful API for job management and user operations
- **Scraper Engine**: Puppeteer-based web scraping with job queue processing
- **Redis Queue**: BullMQ job queue for asynchronous processing
- **MongoDB**: Database for storing jobs, users, and results

### Web Scraping Capabilities
- Full page screenshots
- Content extraction (text, links, images)
- Custom CSS selector data extraction
- Configurable timeouts and wait conditions
- Progress tracking and real-time updates

### Job Queue Features
- Asynchronous job processing
- Retry mechanisms with exponential backoff
- Job progress tracking
- Queue statistics and monitoring
- Horizontal scaling support

## 📚 API Endpoints

### User Management
- `GET /api/users` - List all users
- `POST /api/users` - Create a new user

### Job Management
- `POST /api/queue/scrape` - Create a new scrape job
- `GET /api/queue/job/:jobId` - Get job status and results
- `GET /api/queue/jobs` - List jobs with pagination
- `GET /api/queue/stats` - Get queue statistics

### Health Checks
- `GET /health` - Service health status
- `GET /` - Service information

## 🔧 Configuration

### Environment Variables

The platform uses `.env.docker` for Docker configuration. Key variables:

```env
# Database
MONGO_URI=your-mongodb-atlas-connection-string
MONGO_DB_NAME=web-analyzer

# Services
API_SERVER_PORT=5000
SCRAPER_ENGINE_PORT=5001

# Redis (automatically configured for Docker)
REDIS_URL=redis://redis:6379

# Authentication
JWT_ACCESS_TOKEN_SECRET=your-secret-key
JWT_REFRESH_TOKEN_SECRET=your-refresh-secret-key
```

### MongoDB Setup

1. Create a MongoDB Atlas account
2. Create a new cluster
3. Get your connection string
4. Update `MONGO_URI` in `.env.docker`

## 🐳 Docker Setup

The platform is fully containerized with Docker:

### Development Environment
- Hot reload for code changes
- Debug ports exposed (9229, 9230)
- Development tools included
- Volume mounts for live editing

### Production Environment
- Multi-stage builds for optimization
- Horizontal scaling support
- Load balancing with Nginx
- Health checks and auto-restart

## 📖 Documentation

- **[Development Guide](DEVELOPMENT_GUIDE.md)** - Development setup and troubleshooting
- **[Docker Setup Guide](DOCKER_SETUP.md)** - Comprehensive Docker documentation
- **[Job Queue Integration](JOB_QUEUE_INTEGRATION.md)** - Queue system documentation
- **[Commands Reference](COMMANDS.md)** - All available commands

## 🧪 Testing

### Run Integration Tests
```bash
npm run docker:test
```

### Manual Testing
```bash
# Create a user
curl -X POST http://localhost:5000/api/users \
  -H "Content-Type: application/json" \
  -d '{"name": "Test User", "email": "<EMAIL>"}'

# Create a scrape job
curl -X POST http://localhost:5000/api/queue/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "userId": "USER_ID_FROM_ABOVE",
    "options": {"screenshot": true}
  }'

# Check job status
curl http://localhost:5000/api/queue/job/JOB_ID_FROM_ABOVE
```

## 🔍 Monitoring

### View Logs
```bash
npm run docker:logs
```

### Monitor Queues
Visit http://localhost:8081 for Redis Commander interface

### Container Stats
```bash
docker stats
```

## 🚨 Troubleshooting

### Common Issues

1. **Services won't start**
   ```bash
   npm run docker:clean  # Clean up
   npm run setup         # Re-run setup
   ```

2. **Port conflicts**
   - Check if ports 5000, 5001, 6379 are available
   - Stop other services using these ports

3. **MongoDB connection issues**
   - Verify your MongoDB Atlas connection string
   - Check network connectivity
   - Ensure database user has proper permissions

4. **Docker issues**
   - Ensure Docker is running
   - Check Docker has enough memory (4GB+ recommended)
   - Try restarting Docker

### Get Help
```bash
npm run docker:logs    # View service logs
docker ps              # Check container status
docker system df       # Check Docker disk usage
```

## Development

This is a monorepo using Turborepo for build orchestration. Each package and application can be developed independently while sharing common dependencies and build configurations.
