"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const users_1 = __importDefault(require("./routes/users"));
const scrape_jobs_1 = __importDefault(require("./routes/scrape-jobs"));
const app = (0, express_1.default)();
// Middleware
app.use((0, cors_1.default)());
app.use((0, morgan_1.default)("combined"));
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.get("/health", (req, res) => {
    res.status(200).json({
        status: "ok",
        service: "api-server",
        timestamp: new Date().toISOString(),
        database: "connected", // We'll update this to check actual DB status later
    });
});
// API Routes
app.use("/api/users", users_1.default);
app.use("/api/scrape-jobs", scrape_jobs_1.default);
// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        status: "error",
        message: "Something went wrong!",
        ...(process.env.NODE_ENV === "development" && { error: err.message })
    });
});
exports.default = app;
