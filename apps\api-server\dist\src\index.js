"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const app_1 = __importDefault(require("./app"));
const db_1 = require("./config/db");
const config_1 = require("@web-analyzer/config");
// Load environment variables
dotenv_1.default.config();
const PORT = process.env.API_SERVER_PORT || config_1.config.apiServerPort || 5000;
// Initialize database connection
const startServer = async () => {
    try {
        // Connect to database (non-blocking)
        const dbConnected = await (0, db_1.connectDB)();
        app_1.default.get("/", (req, res) => {
            res.json({
                message: "API Server is running!",
                database: dbConnected ? "connected" : "disconnected",
                timestamp: new Date().toISOString()
            });
        });
        app_1.default.listen(PORT, () => {
            console.log(`🚀 API Server running on port ${PORT}`);
            console.log(`📊 Database status: ${dbConnected ? "✅ Connected" : "❌ Disconnected"}`);
        });
    }
    catch (error) {
        console.error("Failed to start server:", error);
        process.exit(1);
    }
};
startServer();
