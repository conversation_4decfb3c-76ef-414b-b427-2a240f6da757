"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_models_1 = require("@web-analyzer/db-models");
const router = express_1.default.Router();
// GET /api/scrape-jobs - Get all scrape jobs
router.get('/', async (req, res) => {
    try {
        const jobs = await db_models_1.ScrapeJob.find()
            .populate('userId', 'name email')
            .select('-__v')
            .sort({ createdAt: -1 });
        res.json({
            success: true,
            data: jobs
        });
    }
    catch (error) {
        console.error('Error fetching scrape jobs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch scrape jobs'
        });
    }
});
// POST /api/scrape-jobs - Create a new scrape job
router.post('/', async (req, res) => {
    try {
        const { url, userId } = req.body;
        if (!url || !userId) {
            return res.status(400).json({
                success: false,
                message: 'URL and userId are required'
            });
        }
        const job = new db_models_1.ScrapeJob({ url, userId });
        await job.save();
        res.status(201).json({
            success: true,
            data: job
        });
    }
    catch (error) {
        console.error('Error creating scrape job:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create scrape job'
        });
    }
});
exports.default = router;
