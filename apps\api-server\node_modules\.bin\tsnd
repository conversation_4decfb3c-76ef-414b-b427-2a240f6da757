#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/ts-node-dev@2.0.0_@swc+core_901b384854d6d9b880da11cce6cc498b/node_modules/ts-node-dev/lib/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/ts-node-dev@2.0.0_@swc+core_901b384854d6d9b880da11cce6cc498b/node_modules/ts-node-dev/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/ts-node-dev@2.0.0_@swc+core_901b384854d6d9b880da11cce6cc498b/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/ts-node-dev@2.0.0_@swc+core_901b384854d6d9b880da11cce6cc498b/node_modules/ts-node-dev/lib/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/ts-node-dev@2.0.0_@swc+core_901b384854d6d9b880da11cce6cc498b/node_modules/ts-node-dev/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/ts-node-dev@2.0.0_@swc+core_901b384854d6d9b880da11cce6cc498b/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node-dev/lib/bin.js" "$@"
else
  exec node  "$basedir/../ts-node-dev/lib/bin.js" "$@"
fi
