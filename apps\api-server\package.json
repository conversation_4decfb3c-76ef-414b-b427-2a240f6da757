{"name": "api-server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "morgan": "^1.10.0", "mongoose": "^8.3.2", "@web-analyzer/shared-logic": "workspace:*", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.7", "@types/node": "^20.11.30", "@types/uuid": "^9.0.0", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}}