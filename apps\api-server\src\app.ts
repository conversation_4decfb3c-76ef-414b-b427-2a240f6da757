import express from "express";
import cors from "cors";
import morgan from "morgan";
import usersRouter from "./routes/users";
import scrapeJobsRouter from "./routes/scrape-jobs";
import queueRouter from "./routes/queue";

const app = express();

// Middleware
app.use(cors());
app.use(morgan("combined"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.get("/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    service: "api-server",
    timestamp: new Date().toISOString(),
    database: "connected", // We'll update this to check actual DB status later
  });
});

// API Routes
app.use("/api/users", usersRouter);
app.use("/api/scrape-jobs", scrapeJobsRouter);
app.use("/api/queue", queueRouter);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    status: "error",
    message: "Something went wrong!",
    ...(process.env.NODE_ENV === "development" && { error: err.message })
  });
});

export default app;
