import dotenv from "dotenv";
import app from "./app";
import { connectDB } from "./config/db";
import { config } from "@web-analyzer/config";

// Load environment variables
dotenv.config();

const PORT = process.env.API_SERVER_PORT || config.apiServerPort || 5000;

// Initialize database connection
const startServer = async () => {
  try {
    // Connect to database (non-blocking)
    const dbConnected = await connectDB();

    app.get("/", (req, res) => {
      res.json({
        message: "API Server is running!",
        database: dbConnected ? "connected" : "disconnected",
        timestamp: new Date().toISOString()
      });
    });

    app.listen(PORT, () => {
      console.log(`🚀 API Server running on port ${PORT}`);
      console.log(`📊 Database status: ${dbConnected ? "✅ Connected" : "❌ Disconnected"}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

startServer();
