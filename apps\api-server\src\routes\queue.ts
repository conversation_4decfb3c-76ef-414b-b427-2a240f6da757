import express from 'express';
import { addScrapeJob, getJobStatus, getQueueStats } from '@web-analyzer/shared-logic';
import { ScrapeJob } from '@web-analyzer/db-models';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// POST /api/queue/scrape - Add a new scrape job to the queue
router.post('/scrape', async (req, res) => {
  try {
    const { url, userId, options } = req.body;

    if (!url || !userId) {
      return res.status(400).json({
        success: false,
        message: 'URL and userId are required'
      });
    }

    // Generate a unique job ID
    const jobId = uuidv4();

    // Create database record
    const scrapeJob = new ScrapeJob({
      url,
      userId,
      status: 'pending',
    });
    await scrapeJob.save();

    // Add job to queue
    const queueJob = await addScrapeJob({
      url,
      userId,
      jobId: scrapeJob._id?.toString() || '',
      options: options || {}
    });

    // Update database record with queue job ID
    scrapeJob.set('queueJobId', queueJob.id);
    await scrapeJob.save();

    res.status(201).json({
      success: true,
      data: {
        jobId: scrapeJob._id?.toString(),
        queueJobId: queueJob.id,
        url,
        status: 'pending',
        createdAt: scrapeJob.createdAt
      }
    });
  } catch (error: any) {
    console.error('Error adding scrape job to queue:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add scrape job to queue',
      error: error.message
    });
  }
});

// GET /api/queue/job/:jobId - Get job status
router.get('/job/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;

    // Get job from database
    const dbJob = await ScrapeJob.findById(jobId);
    if (!dbJob) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    // Get queue job status if available
    let queueStatus = null;
    if (dbJob.queueJobId) {
      queueStatus = await getJobStatus(dbJob.queueJobId);
    }

    res.json({
      success: true,
      data: {
        jobId: dbJob._id,
        url: dbJob.url,
        status: dbJob.status,
        result: dbJob.result,
        error: dbJob.error,
        createdAt: dbJob.createdAt,
        updatedAt: dbJob.updatedAt,
        queueStatus: queueStatus
      }
    });
  } catch (error: any) {
    console.error('Error getting job status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get job status',
      error: error.message
    });
  }
});

// GET /api/queue/stats - Get queue statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await getQueueStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error: any) {
    console.error('Error getting queue stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get queue stats',
      error: error.message
    });
  }
});

// GET /api/queue/jobs - Get all jobs from database
router.get('/jobs', async (req, res) => {
  try {
    const { status, limit = 50, offset = 0 } = req.query;

    const filter: any = {};
    if (status) {
      filter.status = status;
    }

    const jobs = await ScrapeJob.find(filter)
      .populate('userId', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit as string))
      .skip(parseInt(offset as string));

    const total = await ScrapeJob.countDocuments(filter);

    res.json({
      success: true,
      data: {
        jobs,
        pagination: {
          total,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: total > parseInt(offset as string) + parseInt(limit as string)
        }
      }
    });
  } catch (error: any) {
    console.error('Error getting jobs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get jobs',
      error: error.message
    });
  }
});

export default router;
