import express from 'express';
import { ScrapeJob } from '@web-analyzer/db-models';

const router = express.Router();

// GET /api/scrape-jobs - Get all scrape jobs
router.get('/', async (req, res) => {
  try {
    const jobs = await ScrapeJob.find()
      .populate('userId', 'name email')
      .select('-__v')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: jobs
    });
  } catch (error) {
    console.error('Error fetching scrape jobs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch scrape jobs'
    });
  }
});

// POST /api/scrape-jobs - Create a new scrape job
router.post('/', async (req, res) => {
  try {
    const { url, userId } = req.body;

    if (!url || !userId) {
      return res.status(400).json({
        success: false,
        message: 'URL and userId are required'
      });
    }

    const job = new ScrapeJob({ url, userId });
    await job.save();

    res.status(201).json({
      success: true,
      data: job
    });
  } catch (error) {
    console.error('Error creating scrape job:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create scrape job'
    });
  }
});

export default router;
