"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = require("./config/db");
const config_1 = require("@web-analyzer/config");
// Load environment variables
dotenv_1.default.config();
const PORT = process.env.SCRAPER_ENGINE_PORT || config_1.config.scraperEnginePort || 5001;
// Initialize scraper engine
const startScraperEngine = async () => {
    try {
        // Connect to database (non-blocking)
        const dbConnected = await (0, db_1.connectDB)();
        console.log(`🚀 Scraper Engine started on port ${PORT}`);
        console.log(`📊 Database status: ${dbConnected ? "✅ Connected" : "❌ Disconnected"}`);
        // Add your scraper logic here
    }
    catch (error) {
        console.error("Failed to start scraper engine:", error);
        process.exit(1);
    }
};
startScraperEngine();
