#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/esm/puppeteer/node/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/esm/puppeteer/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/esm/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/esm/puppeteer/node/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/esm/puppeteer/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/esm/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/lib/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules/puppeteer/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/puppeteer@22.15.0_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../puppeteer/lib/esm/puppeteer/node/cli.js" "$@"
else
  exec node  "$basedir/../puppeteer/lib/esm/puppeteer/node/cli.js" "$@"
fi
