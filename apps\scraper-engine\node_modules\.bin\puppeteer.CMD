@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\esm\puppeteer\node\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\esm\puppeteer\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\esm\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\esm\puppeteer\node\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\esm\puppeteer\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\esm\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\lib\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules\puppeteer\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\puppeteer@22.15.0_typescript@5.8.3\node_modules;C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\puppeteer\lib\esm\puppeteer\node\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\puppeteer\lib\esm\puppeteer\node\cli.js" %*
)
