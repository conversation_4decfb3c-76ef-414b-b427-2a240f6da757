{"name": "scraper-engine", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"puppeteer": "^22.0.0", "bullmq": "^4.12.0", "dotenv": "^16.3.1", "ioredis": "^5.3.2", "express": "^4.18.2", "cors": "^2.8.5", "morgan": "^1.10.0", "@web-analyzer/shared-logic": "workspace:*"}, "devDependencies": {"@types/node": "^20.11.30", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}}