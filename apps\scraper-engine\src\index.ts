import dotenv from "dotenv";
import { connectDB } from "./config/db";
import { config } from "@web-analyzer/config";

// Load environment variables
dotenv.config();

const PORT = process.env.SCRAPER_ENGINE_PORT || config.scraperEnginePort || 5001;

// Initialize scraper engine
const startScraperEngine = async () => {
    try {
        // Connect to database (non-blocking)
        const dbConnected = await connectDB();

        console.log(`🚀 Scraper Engine started on port ${PORT}`);
        console.log(`📊 Database status: ${dbConnected ? "✅ Connected" : "❌ Disconnected"}`);

        // Add your scraper logic here

    } catch (error) {
        console.error("Failed to start scraper engine:", error);
        process.exit(1);
    }
};

startScraperEngine();
