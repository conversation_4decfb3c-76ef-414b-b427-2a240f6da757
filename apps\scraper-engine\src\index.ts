import { connectDB } from "./config/db";

import { config } from "@web-analyzer/config";

const PORT = process.env.SCRAPER_ENGINE_PORT || config.scraperEnginePort || 5001;

// Initialize scraper engine
const startScraperEngine = async () => {
    try {
        // Connect to database (non-blocking)
        const dbConnected = await connectDB();
        const app = express();

        app.get("/", (req, res) => {
            res.json({
                message: "Scraper Engine is running!",
                database: dbConnected ? "connected" : "disconnected",
                timestamp: new Date().toISOString()
            });
        });


        console.log(`🚀 Scraper Engine started on port ${PORT}`);
        console.log(`📊 Database status: ${dbConnected ? "Connected" : "Disconnected"}`);

        // Add your scraper logic here

    } catch (error) {
        console.error("Failed to start scraper engine:", error);
        process.exit(1);
    }
};

startScraperEngine();
