import { connectDB } from "./config/db";
import express from "express";
import cors from "cors";
import morgan from "morgan";
import { config } from "@web-analyzer/config";
import { scrapeWorker } from "./workers/scrapeWorker";
import { getQueueStats } from "@web-analyzer/shared-logic";

const PORT = process.env.SCRAPER_ENGINE_PORT || config.scraperEnginePort || 5001;

// Initialize scraper engine
const startScraperEngine = async () => {
    try {
        // Connect to database (non-blocking)
        const dbConnected = await connectDB();
        const app = express();

        // Middleware
        app.use(cors());
        app.use(morgan("combined"));
        app.use(express.json());
        app.use(express.urlencoded({ extended: true }));

        // Routes
        app.get("/", (req, res) => {
            res.json({
                message: "Scraper Engine is running!",
                database: dbConnected ? "connected" : "disconnected",
                timestamp: new Date().toISOString()
            });
        });

        app.get("/health", (req, res) => {
            res.status(200).json({
                status: "ok",
                service: "scraper-engine",
                timestamp: new Date().toISOString(),
                database: dbConnected ? "connected" : "disconnected",
                worker: "running"
            });
        });

        // Queue stats endpoint
        app.get("/queue/stats", async (req, res) => {
            try {
                const stats = await getQueueStats();
                res.json({
                    success: true,
                    data: stats
                });
            } catch (error: any) {
                res.status(500).json({
                    success: false,
                    message: "Failed to get queue stats",
                    error: error.message
                });
            }
        });

        // Start the HTTP server
        app.listen(PORT, () => {
            console.log(`🚀 Scraper Engine HTTP server running on port ${PORT}`);
            console.log(`📊 Database status: ${dbConnected ? "Connected" : "Disconnected"}`);
            console.log(`⚡ Worker started and listening for jobs...`);
        });

        // Graceful shutdown
        process.on('SIGTERM', async () => {
            console.log('🔄 Shutting down gracefully...');
            await scrapeWorker.close();
            process.exit(0);
        });

        process.on('SIGINT', async () => {
            console.log('🔄 Shutting down gracefully...');
            await scrapeWorker.close();
            process.exit(0);
        });

    } catch (error) {
        console.error("Failed to start scraper engine:", error);
        process.exit(1);
    }
};

startScraperEngine();
