import { Worker, Job } from 'bullmq';
import puppeteer from 'puppeteer';
import { redis<PERSON>on<PERSON>ion, ScrapeJobData, ScrapeJobResult } from '@web-analyzer/shared-logic';
import { ScrapeJob } from '@web-analyzer/db-models';

// Create the worker
export const scrapeWorker = new Worker<ScrapeJobData, ScrapeJobResult>(
  'scrape-jobs',
  async (job: Job<ScrapeJobData>) => {
    const { url, userId, jobId, options = {} } = job.data;
    
    console.log(`🔄 Processing scrape job ${job.id} for URL: ${url}`);
    
    // Update job status in database
    await updateJobStatus(jobId, 'running');
    
    let browser;
    try {
      // Update progress
      await job.updateProgress(10);
      
      // Launch browser
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const page = await browser.newPage();
      await job.updateProgress(20);
      
      // Set user agent
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // Navigate to URL
      const startTime = Date.now();
      await page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: options.timeout || 30000
      });
      await job.updateProgress(50);
      
      // Wait for specific selector if provided
      if (options.waitForSelector) {
        await page.waitForSelector(options.waitForSelector, { timeout: 10000 });
      }
      
      await job.updateProgress(60);
      
      // Extract data
      const result: any = {
        url,
        timestamp: new Date().toISOString(),
        loadTime: Date.now() - startTime,
        userAgent: await page.evaluate(() => navigator.userAgent)
      };
      
      // Extract title
      result.title = await page.title();
      
      // Extract text content if requested
      if (options.extractText !== false) {
        result.content = await page.evaluate(() => document.body.innerText);
      }
      
      await job.updateProgress(70);
      
      // Extract links if requested
      if (options.extractLinks) {
        result.links = await page.evaluate(() => {
          const links = Array.from(document.querySelectorAll('a[href]'));
          return links.map(link => (link as HTMLAnchorElement).href);
        });
      }
      
      // Extract images if requested
      if (options.extractImages) {
        result.images = await page.evaluate(() => {
          const images = Array.from(document.querySelectorAll('img[src]'));
          return images.map(img => (img as HTMLImageElement).src);
        });
      }
      
      await job.updateProgress(80);
      
      // Extract custom data if selectors provided
      if (options.customSelectors) {
        result.customData = {};
        for (const [key, selector] of Object.entries(options.customSelectors)) {
          try {
            result.customData[key] = await page.evaluate((sel) => {
              const element = document.querySelector(sel);
              return element ? element.textContent?.trim() : null;
            }, selector);
          } catch (error) {
            console.warn(`Failed to extract data for selector ${selector}:`, error);
            result.customData[key] = null;
          }
        }
      }
      
      // Take screenshot if requested
      if (options.screenshot) {
        const screenshot = await page.screenshot({
          fullPage: options.fullPage || false,
          encoding: 'base64'
        });
        result.screenshot = screenshot;
      }
      
      await job.updateProgress(90);
      
      // Close browser
      await browser.close();
      
      // Update job status in database
      await updateJobStatus(jobId, 'completed', result);
      
      await job.updateProgress(100);
      
      console.log(`✅ Completed scrape job ${job.id} for URL: ${url}`);
      
      return {
        jobId,
        url,
        status: 'completed',
        data: result,
        completedAt: new Date().toISOString()
      };
      
    } catch (error: any) {
      console.error(`❌ Failed scrape job ${job.id} for URL: ${url}:`, error);
      
      // Close browser if it was opened
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('Error closing browser:', closeError);
        }
      }
      
      // Update job status in database
      await updateJobStatus(jobId, 'failed', null, error.message);
      
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 3, // Process up to 3 jobs concurrently
  }
);

// Helper function to update job status in database
async function updateJobStatus(
  jobId: string, 
  status: 'pending' | 'running' | 'completed' | 'failed',
  result?: any,
  error?: string
) {
  try {
    const updateData: any = { status };
    if (result) updateData.result = result;
    if (error) updateData.error = error;
    
    await ScrapeJob.findByIdAndUpdate(jobId, updateData);
  } catch (dbError) {
    console.error('Failed to update job status in database:', dbError);
  }
}

// Worker event handlers
scrapeWorker.on('completed', (job, result) => {
  console.log(`🎉 Job ${job.id} completed successfully`);
});

scrapeWorker.on('failed', (job, err) => {
  console.error(`💥 Job ${job?.id} failed:`, err.message);
});

scrapeWorker.on('progress', (job, progress) => {
  console.log(`📊 Job ${job.id} progress: ${progress}%`);
});

export default scrapeWorker;
