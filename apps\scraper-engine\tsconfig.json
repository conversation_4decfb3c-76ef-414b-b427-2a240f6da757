{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "composite": true, "declaration": true, "declarationMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"], "references": [{"path": "../../packages/db-models"}, {"path": "../../packages/config"}]}