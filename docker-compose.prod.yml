version: '3.8'

services:
  # Redis service for job queue
  redis:
    image: redis:7-alpine
    container_name: web-analyzer-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    networks:
      - web-analyzer-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # API Server
  api-server:
    build:
      context: ./apps/api-server
      dockerfile: Dockerfile
      target: production
    container_name: web-analyzer-api-server-prod
    restart: always
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - API_SERVER_PORT=5000
      - MONGO_URI=${MONGO_URI}
      - MONGO_DB_NAME=${MONGO_DB_NAME}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_ACCESS_TOKEN_SECRET=${JWT_ACCESS_TOKEN_SECRET}
      - JWT_REFRESH_TOKEN_SECRET=${JWT_REFRESH_TOKEN_SECRET}
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - web-analyzer-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # Scraper Engine
  scraper-engine:
    build:
      context: ./apps/scraper-engine
      dockerfile: Dockerfile
      target: production
    container_name: web-analyzer-scraper-engine-prod
    restart: always
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - SCRAPER_ENGINE_PORT=5001
      - MONGO_URI=${MONGO_URI}
      - MONGO_DB_NAME=${MONGO_DB_NAME}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      # Puppeteer configuration for Docker
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
    depends_on:
      redis:
        condition: service_healthy
      api-server:
        condition: service_healthy
    networks:
      - web-analyzer-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Security options for Puppeteer
    security_opt:
      - seccomp:unconfined
    shm_size: 2gb
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: web-analyzer-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api-server
      - scraper-engine
    networks:
      - web-analyzer-network

volumes:
  redis_data:
    driver: local

networks:
  web-analyzer-network:
    driver: bridge
