#!/usr/bin/env node

/**
 * Development Startup Script
 * Ensures Redis is running and starts both API server and scraper engine
 */

const { execSync, spawn } = require('child_process');
const path = require('path');

// Change to project root directory (one level up from infrastructure)
process.chdir(path.join(__dirname, '..'));

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Helper function to check if command exists
function commandExists(command) {
  try {
    execSync(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'ignore' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Check if Redis is accessible
function isRedisAccessible() {
  try {
    execSync('docker exec web-analyzer-redis-dev redis-cli ping', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Start Redis if needed
async function ensureRedis() {
  log.info('Checking Redis status...');

  if (isRedisAccessible()) {
    log.success('Redis is already running');
    return true;
  }

  log.info('Starting Redis...');
  try {
    execSafe('node scripts/start-redis.js');
    return true;
  } catch (error) {
    log.error('Failed to start Redis');
    return false;
  }
}

// Build shared packages
async function buildSharedPackages() {
  log.info('Building shared packages...');

  const packages = [
    '@web-analyzer/config',
    '@web-analyzer/db-models',
    '@web-analyzer/shared-logic'
  ];

  for (const pkg of packages) {
    try {
      log.info(`Building ${pkg}...`);
      execSafe(`pnpm --filter ${pkg} build`, { silent: true });
      log.success(`Built ${pkg}`);
    } catch (error) {
      log.warning(`Failed to build ${pkg}, continuing...`);
    }
  }
}

// Start development servers
function startDevServers() {
  log.info('Starting development servers...');

  // Start API server
  const apiServer = spawn('pnpm', ['--filter', 'api-server', 'dev'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true
  });

  // Start scraper engine
  const scraperEngine = spawn('pnpm', ['--filter', 'scraper-engine', 'dev'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true
  });

  // Handle API server output
  apiServer.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.blue}[API]${colors.reset} ${line.trim()}`);
      }
    });
  });

  apiServer.stderr.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.red}[API ERROR]${colors.reset} ${line.trim()}`);
      }
    });
  });

  // Handle scraper engine output
  scraperEngine.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.green}[SCRAPER]${colors.reset} ${line.trim()}`);
      }
    });
  });

  scraperEngine.stderr.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.red}[SCRAPER ERROR]${colors.reset} ${line.trim()}`);
      }
    });
  });

  // Handle process exits
  apiServer.on('close', (code) => {
    if (code !== 0) {
      log.error(`API server exited with code ${code}`);
    }
  });

  scraperEngine.on('close', (code) => {
    if (code !== 0) {
      log.error(`Scraper engine exited with code ${code}`);
    }
  });

  // Handle process termination
  process.on('SIGINT', () => {
    log.info('Shutting down development servers...');
    apiServer.kill('SIGINT');
    scraperEngine.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    log.info('Shutting down development servers...');
    apiServer.kill('SIGTERM');
    scraperEngine.kill('SIGTERM');
    process.exit(0);
  });

  return { apiServer, scraperEngine };
}

// Main function
async function main() {
  log.title('🚀 Development Environment Startup');
  log.title('==================================');
  console.log();

  try {
    // Check prerequisites
    if (!commandExists('pnpm')) {
      log.error('pnpm is not installed');
      log.info('Please install pnpm: npm install -g pnpm');
      process.exit(1);
    }

    if (!commandExists('docker')) {
      log.error('Docker is not installed');
      log.info('Please install Docker to run Redis');
      process.exit(1);
    }

    // Ensure Redis is running
    if (!(await ensureRedis())) {
      log.error('Failed to start Redis');
      log.info('You can try using Docker: npm start');
      process.exit(1);
    }

    // Build shared packages
    await buildSharedPackages();

    console.log();
    log.success('🎉 Starting development servers...');
    console.log();
    log.info('Services will be available at:');
    log.info('  • API Server: http://localhost:5000');
    log.info('  • Scraper Engine: http://localhost:5001');
    console.log();
    log.info('Press Ctrl+C to stop all services');
    console.log();

    // Start development servers
    const { apiServer, scraperEngine } = startDevServers();

    // Keep the process alive
    await new Promise(() => { });

  } catch (error) {
    log.error('Development startup failed!');
    log.error(error.message);
    console.log();
    log.info('Alternative options:');
    log.info('1. Use Docker: npm start');
    log.info('2. Start Redis manually: npm run redis:start');
    log.info('3. Run services individually:');
    log.info('   - pnpm --filter api-server dev');
    log.info('   - pnpm --filter scraper-engine dev');
    process.exit(1);
  }
}

// Run main function
main().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
