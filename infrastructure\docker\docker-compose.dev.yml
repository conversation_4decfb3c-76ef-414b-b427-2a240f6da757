version: "3.8"

services:
  # Redis service for development
  redis:
    image: redis:7-alpine
    container_name: web-analyzer-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - web-analyzer-dev-network

  # API Server Development
  api-server:
    build:
      context: ../../
      dockerfile: apps/api-server/Dockerfile
      target: development
    container_name: web-analyzer-api-server-dev
    restart: unless-stopped
    ports:
      - "5000:5000"
      - "9229:9229" # Node.js debugging port
    environment:
      - NODE_ENV=development
      - API_SERVER_PORT=5000
      - MONGO_URI=${MONGO_URI}
      - MONGO_DB_NAME=${MONGO_DB_NAME:-web-analyzer-dev}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=
      - JWT_ACCESS_TOKEN_SECRET=${JWT_ACCESS_TOKEN_SECRET:-dev-secret}
      - JWT_REFRESH_TOKEN_SECRET=${JWT_REFRESH_TOKEN_SECRET:-dev-refresh-secret}
      # Enable debugging
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    volumes:
      - ../../apps/api-server/src:/app/src
      - ../../packages:/app/packages
      - /app/node_modules
      - /app/packages/node_modules
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - web-analyzer-dev-network
    command: ["sh", "-c", "cd apps/api-server && npx ts-node-dev src/index.ts"]

  # Scraper Engine Development
  scraper-engine:
    build:
      context: ../../
      dockerfile: apps/scraper-engine/Dockerfile
      target: development
    container_name: web-analyzer-scraper-engine-dev
    restart: unless-stopped
    ports:
      - "5001:5001"
      - "9230:9230" # Node.js debugging port
    environment:
      - NODE_ENV=development
      - SCRAPER_ENGINE_PORT=5001
      - MONGO_URI=${MONGO_URI}
      - MONGO_DB_NAME=${MONGO_DB_NAME:-web-analyzer-dev}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=
      # Puppeteer configuration for Docker
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
      # Enable debugging
      - NODE_OPTIONS=--inspect=0.0.0.0:9230
    volumes:
      - ../../apps/scraper-engine/src:/app/src
      - ../../packages:/app/packages
      - /app/node_modules
      - /app/packages/node_modules
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - web-analyzer-dev-network
    # Security options for Puppeteer
    security_opt:
      - seccomp:unconfined
    shm_size: 2gb
    command:
      ["sh", "-c", "cd apps/scraper-engine && npx ts-node-dev src/index.ts"]

  # Redis Commander for development
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: web-analyzer-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - web-analyzer-dev-network

  # MongoDB Express (if using local MongoDB)
  mongo-express:
    image: mongo-express:latest
    container_name: web-analyzer-mongo-express-dev
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      - ME_CONFIG_MONGODB_URL=${MONGO_URI}
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    networks:
      - web-analyzer-dev-network
    profiles:
      - tools

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: web-analyzer-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - web-analyzer-dev-network
    profiles:
      - tools

volumes:
  redis_dev_data:
    driver: local

networks:
  web-analyzer-dev-network:
    driver: bridge
