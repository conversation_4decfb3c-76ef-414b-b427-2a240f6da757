version: "3.8"

services:
  # Redis service for job queue
  redis:
    image: redis:7-alpine
    container_name: web-analyzer-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    networks:
      - web-analyzer-network

  # API Server
  api-server:
    build:
      context: ../../apps/api-server
      dockerfile: Dockerfile
      target: development
    container_name: web-analyzer-api-server
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - API_SERVER_PORT=5000
      - MONGO_URI=${MONGO_URI}
      - MONGO_DB_NAME=${MONGO_DB_NAME}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - JWT_ACCESS_TOKEN_SECRET=${JWT_ACCESS_TOKEN_SECRET}
      - JWT_REFRESH_TOKEN_SECRET=${JWT_REFRESH_TOKEN_SECRET}
    volumes:
      - ./apps/api-server:/app
      - ./packages:/app/packages
      - /app/node_modules
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - web-analyzer-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Scraper Engine
  scraper-engine:
    build:
      context: ../../apps/scraper-engine
      dockerfile: Dockerfile
      target: development
    container_name: web-analyzer-scraper-engine
    restart: unless-stopped
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=development
      - SCRAPER_ENGINE_PORT=5001
      - MONGO_URI=${MONGO_URI}
      - MONGO_DB_NAME=${MONGO_DB_NAME}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      # Puppeteer configuration for Docker
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
    volumes:
      - ../../apps/scraper-engine:/app
      - ../../packages:/app/packages
      - /app/node_modules
    depends_on:
      redis:
        condition: service_healthy
      api-server:
        condition: service_healthy
    networks:
      - web-analyzer-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Security options for Puppeteer
    security_opt:
      - seccomp:unconfined
    shm_size: 2gb

  # Redis Commander (Optional - for Redis management UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: web-analyzer-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    depends_on:
      - redis
    networks:
      - web-analyzer-network
    profiles:
      - tools

volumes:
  redis_data:
    driver: local

networks:
  web-analyzer-network:
    driver: bridge
