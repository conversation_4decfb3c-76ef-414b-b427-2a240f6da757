#!/usr/bin/env node

/**
 * Web Analyzer Platform - Interactive Setup Script
 * Handles initial project setup and configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`),
  subtitle: (msg) => console.log(`${colors.magenta}${msg}${colors.reset}`)
};

// Helper function to check if command exists
function commandExists(command) {
  try {
    execSync(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'ignore' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to ask questions
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Check prerequisites
async function checkPrerequisites() {
  log.subtitle('Step 1: Checking Prerequisites');
  
  const requirements = [
    { name: 'Node.js', command: 'node', version: '--version' },
    { name: 'npm', command: 'npm', version: '--version' },
    { name: 'pnpm', command: 'pnpm', version: '--version' },
    { name: 'Docker', command: 'docker', version: '--version' },
    { name: 'Docker Compose', command: 'docker-compose', version: '--version' }
  ];

  let allGood = true;

  for (const req of requirements) {
    if (commandExists(req.command)) {
      try {
        const version = execSafe(`${req.command} ${req.version}`, { silent: true });
        log.success(`✅ ${req.name}: ${version.split('\n')[0]}`);
      } catch {
        log.success(`✅ ${req.name}: Available`);
      }
    } else {
      log.error(`❌ ${req.name}: Not found`);
      allGood = false;
    }
  }

  if (!allGood) {
    console.log();
    log.error('Some prerequisites are missing!');
    log.info('Please install the missing requirements:');
    log.info('• Node.js: https://nodejs.org/');
    log.info('• pnpm: npm install -g pnpm');
    log.info('• Docker: https://docker.com/get-started');
    process.exit(1);
  }

  // Check Docker daemon
  try {
    execSafe('docker info', { silent: true });
    log.success('✅ Docker daemon is running');
  } catch {
    log.error('❌ Docker daemon is not running');
    log.info('Please start Docker and try again.');
    process.exit(1);
  }

  console.log();
}

// Setup environment variables
async function setupEnvironment() {
  log.subtitle('Step 2: Environment Configuration');
  
  if (fs.existsSync('.env.docker')) {
    const overwrite = await askQuestion('Environment file already exists. Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      log.info('Keeping existing environment file');
      console.log();
      return;
    }
  }

  log.info('Creating environment configuration...');
  
  const mongoUri = await askQuestion('MongoDB URI (press Enter for default): ') || 'mongodb://localhost:27017/web-analyzer';
  const redisUrl = await askQuestion('Redis URL (press Enter for default): ') || 'redis://localhost:6379';
  const nodeEnv = await askQuestion('Environment (development/production) [development]: ') || 'development';

  const envContent = `# Web Analyzer Platform - Docker Environment
# Generated by setup script on ${new Date().toISOString()}

# Database Configuration
MONGO_URI=${mongoUri}
REDIS_URL=${redisUrl}

# Application Configuration
NODE_ENV=${nodeEnv}
PORT_API=5000
PORT_SCRAPER=5001
PORT_REDIS_COMMANDER=8081

# Security (change in production)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
SESSION_SECRET=your-super-secret-session-key-change-in-production

# Development Settings
DEBUG=web-analyzer:*
LOG_LEVEL=info

# Docker Settings
COMPOSE_PROJECT_NAME=web-analyzer
`;

  fs.writeFileSync('.env.docker', envContent);
  log.success('Environment file created successfully');
  console.log();
}

// Install dependencies
async function installDependencies() {
  log.subtitle('Step 3: Installing Dependencies');
  
  log.info('Installing workspace dependencies...');
  execSafe('pnpm install');
  
  log.info('Building shared packages...');
  execSafe('pnpm run build');
  
  log.success('Dependencies installed successfully');
  console.log();
}

// Setup Docker services
async function setupDockerServices() {
  log.subtitle('Step 4: Setting up Docker Services');
  
  log.info('Building Docker images...');
  execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml --env-file .env.docker build');
  
  log.success('Docker images built successfully');
  console.log();
}

// Start services
async function startServices() {
  log.subtitle('Step 5: Starting Services');
  
  log.info('Starting all services...');
  execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml --env-file .env.docker up -d');
  
  log.success('Services started successfully');
  log.info('Waiting for services to initialize...');
  
  // Show spinner while waiting
  const spinnerChars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
  let spinnerIndex = 0;
  
  const spinner = setInterval(() => {
    process.stdout.write(`\r${colors.cyan}${spinnerChars[spinnerIndex]}${colors.reset} Initializing services...`);
    spinnerIndex = (spinnerIndex + 1) % spinnerChars.length;
  }, 100);

  await new Promise(resolve => setTimeout(resolve, 45000)); // Wait 45 seconds

  clearInterval(spinner);
  process.stdout.write('\r' + ' '.repeat(50) + '\r'); // Clear spinner line
  log.success('Services initialization completed');
  console.log();
}

// Verify installation
async function verifyInstallation() {
  log.subtitle('Step 6: Verifying Installation');
  log.info('Running integration tests...');

  try {
    execSafe('node infrastructure/scripts/test-docker.js');
  } catch (error) {
    log.warning('Some tests failed, but the basic setup is complete.');
    log.info('You can check the logs with: npm run docker:logs');
  }
}

// Main setup function
async function main() {
  log.title('🚀 Web Analyzer Platform - Interactive Setup');
  log.title('==============================================');
  console.log();
  
  log.info('This script will help you set up the Web Analyzer Platform.');
  log.info('It will check prerequisites, configure environment, and start services.');
  console.log();
  
  const proceed = await askQuestion('Do you want to continue? (Y/n): ');
  if (proceed.toLowerCase() === 'n') {
    log.info('Setup cancelled.');
    rl.close();
    return;
  }
  
  console.log();

  try {
    await checkPrerequisites();
    await setupEnvironment();
    await installDependencies();
    await setupDockerServices();
    await startServices();
    await verifyInstallation();

    console.log();
    log.title('🎉 Setup Complete!');
    log.title('==================');
    console.log();
    log.success('Web Analyzer Platform is now running!');
    console.log();
    log.info('Available services:');
    log.info('  • API Server:        http://localhost:5000');
    log.info('  • Scraper Engine:     http://localhost:5001');
    log.info('  • Redis Commander:    http://localhost:8081');
    console.log();
    log.info('Useful commands:');
    log.info('  • View logs:          npm run docker:logs');
    log.info('  • Stop services:      npm stop');
    log.info('  • Restart services:   npm restart');
    log.info('  • Run tests:          npm run docker:test');
    console.log();
    log.info('For development:');
    log.info('  • Development mode:   npm run dev');
    log.info('  • Test Redis:         npm run redis:test');
    console.log();

  } catch (error) {
    log.error('Setup failed!');
    log.error(error.message);
    console.log();
    log.info('Troubleshooting:');
    log.info('1. Check Docker is running: docker info');
    log.info('2. Check port availability: netstat -an | findstr "5000 5001 6379 8081"');
    log.info('3. Try manual setup: npm start');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run main function
main().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  rl.close();
  process.exit(1);
});
