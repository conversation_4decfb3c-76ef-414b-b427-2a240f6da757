#!/usr/bin/env node

/**
 * Redis Startup Script
 * Starts Redis container for development
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.red}${msg}${colors.reset}`)
};

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'ignore' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Check if Redis is accessible
function isRedisAccessible() {
  try {
    execSafe('docker exec web-analyzer-redis-dev redis-cli ping', { silent: true });
    return true;
  } catch {
    return false;
  }
}

// Check if Redis container exists
function redisContainerExists() {
  try {
    execSafe('docker inspect web-analyzer-redis-dev', { silent: true });
    return true;
  } catch {
    return false;
  }
}

// Main function
async function main() {
  log.title('🔴 Redis Startup Script');
  log.title('======================');
  console.log();

  try {
    // Check if Redis is already accessible
    if (isRedisAccessible()) {
      log.success('Redis is already running and accessible!');
      console.log();
      log.info('Redis connection details:');
      log.info('  • Host: localhost');
      log.info('  • Port: 6379');
      log.info('  • Container: web-analyzer-redis-dev');
      return;
    }

    // Check if container exists but is stopped
    if (redisContainerExists()) {
      log.info('Redis container exists but is stopped');
      log.info('Starting existing Redis container...');
      execSafe('docker start web-analyzer-redis-dev');
    } else {
      log.info('Redis is not running');
      log.info('Starting Redis container...');
      
      // Start Redis container
      execSafe(`docker run -d \
        --name web-analyzer-redis-dev \
        --restart unless-stopped \
        -p 6379:6379 \
        -v redis-data:/data \
        redis:7-alpine \
        redis-server --appendonly yes`);
    }

    log.success('Redis container started successfully');
    
    // Wait for Redis to be ready
    log.info('Waiting for Redis to be ready...');
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      try {
        execSafe('docker exec web-analyzer-redis-dev redis-cli ping', { silent: true });
        break;
      } catch {
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    if (attempts >= maxAttempts) {
      throw new Error('Redis failed to start within 30 seconds');
    }
    
    log.success('Redis is ready!');
    console.log();
    log.success('🎉 Redis is running successfully!');
    console.log();
    log.info('Redis connection details:');
    log.info('  • Host: localhost');
    log.info('  • Port: 6379');
    log.info('  • Container: web-analyzer-redis-dev');
    console.log();
    log.info('Next steps:');
    log.info('  1. Start API Server: pnpm --filter api-server dev');
    log.info('  2. Start Scraper Engine: pnpm --filter scraper-engine dev');
    log.info('  3. Or use Docker: npm start');
    console.log();
    log.info('To stop Redis: docker stop web-analyzer-redis-dev');

  } catch (error) {
    log.error('Failed to start Redis!');
    log.error(error.message);
    console.log();
    log.info('Troubleshooting:');
    log.info('1. Check Docker is running: docker info');
    log.info('2. Check port 6379 is available: netstat -an | findstr 6379');
    log.info('3. Try manual start: docker run -d --name redis-test -p 6379:6379 redis:7-alpine');
    process.exit(1);
  }
}

// Run main function
main().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
