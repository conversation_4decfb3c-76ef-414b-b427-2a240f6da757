@echo off
title Web Analyzer Platform - Docker Tests

echo.
echo 🐳 Web Analyzer Platform - Docker Tests
echo =======================================
echo.

REM Check if Node.js is installed
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if Docker is installed
where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed!
    echo Please install Docker from https://docker.com/get-started
    pause
    exit /b 1
)

echo [INFO] Running Docker integration tests...
echo.

REM Run the Node.js test script
node infrastructure/scripts/test-docker.js

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Some tests failed!
    echo Check the output above for details.
    echo.
    pause
    exit /b 1
)

echo.
echo Tests completed successfully! Press any key to exit...
pause >nul
