#!/usr/bin/env node

/**
 * Docker Integration Test Script
 * Tests all Docker services and their connectivity
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Test HTTP endpoint
async function testEndpoint(url, name, timeout = 5000) {
  return new Promise((resolve) => {
    const startTime = Date.now();

    const testRequest = () => {
      try {
        execSafe(`curl -f -s --max-time 5 ${url}`, { silent: true });
        resolve({ success: true, time: Date.now() - startTime });
      } catch {
        if (Date.now() - startTime < timeout) {
          setTimeout(testRequest, 1000);
        } else {
          resolve({ success: false, time: Date.now() - startTime });
        }
      }
    };

    testRequest();
  });
}

// Main test function
async function runDockerTests() {
  log.title('🐳 Docker Integration Tests');
  log.title('============================');
  console.log();

  let testsPassed = 0;
  let totalTests = 0;

  try {
    // Test 1: Check Docker daemon
    totalTests++;
    log.info('Test 1: Checking Docker daemon...');
    try {
      execSafe('docker info', { silent: true });
      log.success('✅ Docker daemon is running');
      testsPassed++;
    } catch {
      log.error('❌ Docker daemon is not running');
      throw new Error('Docker daemon is not accessible');
    }

    // Test 2: Check Docker Compose
    totalTests++;
    log.info('Test 2: Checking Docker Compose...');
    try {
      execSafe('docker-compose --version', { silent: true });
      log.success('✅ Docker Compose is available');
      testsPassed++;
    } catch {
      log.error('❌ Docker Compose is not available');
    }

    // Test 3: Check if services are running
    totalTests++;
    log.info('Test 3: Checking if services are running...');
    try {
      const result = execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml ps --services --filter "status=running"', { silent: true });
      const runningServices = result.trim().split('\n').filter(s => s.length > 0);

      if (runningServices.length >= 3) { // Expecting at least Redis, API, Scraper
        log.success(`✅ Services are running (${runningServices.length} services)`);
        testsPassed++;
      } else {
        log.warning(`⚠️  Only ${runningServices.length} services running`);
        log.info('Starting services...');
        execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml up -d', { silent: true });
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      }
    } catch {
      log.error('❌ Failed to check service status');
    }

    // Test 4: Test Redis connectivity
    totalTests++;
    log.info('Test 4: Testing Redis connectivity...');
    try {
      execSafe('docker exec web-analyzer-redis-dev redis-cli ping', { silent: true });
      log.success('✅ Redis is accessible');
      testsPassed++;
    } catch {
      log.error('❌ Redis is not accessible');
    }

    // Test 5: Test API Server
    totalTests++;
    log.info('Test 5: Testing API Server (http://localhost:5000)...');
    const apiTest = await testEndpoint('http://localhost:5000/health', 'API Server', 15000);
    if (apiTest.success) {
      log.success(`✅ API Server is responding (${apiTest.time}ms)`);
      testsPassed++;
    } else {
      log.error(`❌ API Server is not responding (timeout after ${apiTest.time}ms)`);
    }

    // Test 6: Test Scraper Engine
    totalTests++;
    log.info('Test 6: Testing Scraper Engine (http://localhost:5001)...');
    const scraperTest = await testEndpoint('http://localhost:5001/health', 'Scraper Engine', 15000);
    if (scraperTest.success) {
      log.success(`✅ Scraper Engine is responding (${scraperTest.time}ms)`);
      testsPassed++;
    } else {
      log.error(`❌ Scraper Engine is not responding (timeout after ${scraperTest.time}ms)`);
    }

    // Test 7: Test Redis Commander
    totalTests++;
    log.info('Test 7: Testing Redis Commander (http://localhost:8081)...');
    const redisCommanderTest = await testEndpoint('http://localhost:8081', 'Redis Commander', 10000);
    if (redisCommanderTest.success) {
      log.success(`✅ Redis Commander is responding (${redisCommanderTest.time}ms)`);
      testsPassed++;
    } else {
      log.warning(`⚠️  Redis Commander is not responding (timeout after ${redisCommanderTest.time}ms)`);
      // Don't fail the test for Redis Commander as it's not critical
      testsPassed++;
    }

    // Test 8: Test container health
    totalTests++;
    log.info('Test 8: Checking container health...');
    try {
      const result = execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml ps', { silent: true });
      if (result.includes('Up') && !result.includes('Exit')) {
        log.success('✅ All containers are healthy');
        testsPassed++;
      } else {
        log.warning('⚠️  Some containers may have issues');
        log.info('Container status:');
        execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml ps');
      }
    } catch {
      log.error('❌ Failed to check container health');
    }

    console.log();
    log.title('📊 Test Results');
    log.title('===============');

    const successRate = Math.round((testsPassed / totalTests) * 100);

    if (testsPassed === totalTests) {
      log.success(`🎉 All tests passed! (${testsPassed}/${totalTests}) - ${successRate}%`);
      log.success('Docker environment is working correctly!');
      console.log();
      log.info('Available services:');
      log.info('  • API Server:        http://localhost:5000');
      log.info('  • Scraper Engine:     http://localhost:5001');
      log.info('  • Redis Commander:    http://localhost:8081');
    } else if (successRate >= 75) {
      log.warning(`⚠️  Most tests passed (${testsPassed}/${totalTests}) - ${successRate}%`);
      log.warning('Docker environment is mostly working, but some issues detected.');
    } else {
      log.error(`❌ Many tests failed (${testsPassed}/${totalTests}) - ${successRate}%`);
      log.error('Docker environment has significant issues.');
      console.log();
      log.info('Troubleshooting:');
      log.info('1. Check logs: npm run docker:logs');
      log.info('2. Restart services: npm restart');
      log.info('3. Clean restart: npm run docker:clean && npm start');
      process.exit(1);
    }

  } catch (error) {
    log.error('Docker integration test failed!');
    log.error(error.message);
    console.log();
    log.info('Troubleshooting:');
    log.info('1. Check Docker is running: docker info');
    log.info('2. Start services: npm start');
    log.info('3. Check logs: npm run docker:logs');
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  runDockerTests().catch(error => {
    log.error(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}
