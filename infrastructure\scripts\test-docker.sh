#!/bin/bash

# Web Analyzer Platform - Docker Tests (Linux/Mac)

echo ""
echo "🐳 Web Analyzer Platform - Docker Tests"
echo "======================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "[ERROR] Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "[ERROR] Docker is not installed!"
    echo "Please install Docker from https://docker.com/get-started"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "[ERROR] Docker Compose is not installed!"
    echo "Please install Docker Compose"
    exit 1
fi

echo "[INFO] Running Docker integration tests..."
echo ""

# Run the Node.js test script
node infrastructure/scripts/test-docker.js

if [ $? -ne 0 ]; then
    echo ""
    echo "[ERROR] Some tests failed!"
    echo "Check the output above for details."
    exit 1
fi

echo ""
echo "Tests completed successfully!"
