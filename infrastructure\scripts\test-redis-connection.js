#!/usr/bin/env node

/**
 * Redis Connection Test Script
 * Tests Redis connectivity and basic operations
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Test Redis connection
async function testRedisConnection() {
  log.title('🔴 Redis Connection Test');
  log.title('========================');
  console.log();

  let testsPassed = 0;
  let totalTests = 0;

  try {
    // Test 1: Check if Redis container is running
    totalTests++;
    log.info('Test 1: Checking if Redis container is running...');
    try {
      const result = execSafe('docker ps --filter "name=web-analyzer-redis-dev" --format "{{.Status}}"', { silent: true });
      if (result.includes('Up')) {
        log.success('✅ Redis container is running');
        testsPassed++;
      } else {
        log.error('❌ Redis container is not running');
      }
    } catch {
      log.error('❌ Redis container is not running');
    }

    // Test 2: Test Redis ping
    totalTests++;
    log.info('Test 2: Testing Redis ping...');
    try {
      const result = execSafe('docker exec web-analyzer-redis-dev redis-cli ping', { silent: true });
      if (result.trim() === 'PONG') {
        log.success('✅ Redis ping successful');
        testsPassed++;
      } else {
        log.error('❌ Redis ping failed');
      }
    } catch {
      log.error('❌ Redis ping failed');
    }

    // Test 3: Test Redis set/get operations
    totalTests++;
    log.info('Test 3: Testing Redis set/get operations...');
    try {
      const testKey = 'test:connection:' + Date.now();
      const testValue = 'test-value-' + Math.random();
      
      // Set value
      execSafe(`docker exec web-analyzer-redis-dev redis-cli set ${testKey} "${testValue}"`, { silent: true });
      
      // Get value
      const result = execSafe(`docker exec web-analyzer-redis-dev redis-cli get ${testKey}`, { silent: true });
      
      if (result.trim() === testValue) {
        log.success('✅ Redis set/get operations successful');
        testsPassed++;
        
        // Clean up test key
        execSafe(`docker exec web-analyzer-redis-dev redis-cli del ${testKey}`, { silent: true });
      } else {
        log.error('❌ Redis set/get operations failed');
      }
    } catch {
      log.error('❌ Redis set/get operations failed');
    }

    // Test 4: Check Redis info
    totalTests++;
    log.info('Test 4: Checking Redis server info...');
    try {
      const result = execSafe('docker exec web-analyzer-redis-dev redis-cli info server', { silent: true });
      if (result.includes('redis_version')) {
        log.success('✅ Redis server info accessible');
        testsPassed++;
        
        // Extract version
        const versionMatch = result.match(/redis_version:([^\r\n]+)/);
        if (versionMatch) {
          log.info(`   Redis version: ${versionMatch[1]}`);
        }
      } else {
        log.error('❌ Redis server info not accessible');
      }
    } catch {
      log.error('❌ Redis server info not accessible');
    }

    // Test 5: Check Redis memory usage
    totalTests++;
    log.info('Test 5: Checking Redis memory usage...');
    try {
      const result = execSafe('docker exec web-analyzer-redis-dev redis-cli info memory', { silent: true });
      if (result.includes('used_memory_human')) {
        log.success('✅ Redis memory info accessible');
        testsPassed++;
        
        // Extract memory usage
        const memoryMatch = result.match(/used_memory_human:([^\r\n]+)/);
        if (memoryMatch) {
          log.info(`   Memory usage: ${memoryMatch[1]}`);
        }
      } else {
        log.error('❌ Redis memory info not accessible');
      }
    } catch {
      log.error('❌ Redis memory info not accessible');
    }

    console.log();
    log.title('📊 Test Results');
    log.title('===============');
    
    if (testsPassed === totalTests) {
      log.success(`🎉 All tests passed! (${testsPassed}/${totalTests})`);
      log.success('Redis is working correctly!');
      console.log();
      log.info('Connection details:');
      log.info('  • Host: localhost');
      log.info('  • Port: 6379');
      log.info('  • Container: web-analyzer-redis-dev');
    } else {
      log.warning(`⚠️  Some tests failed (${testsPassed}/${totalTests})`);
      console.log();
      log.info('Troubleshooting:');
      log.info('1. Start Redis: npm run redis:start');
      log.info('2. Check Docker: docker ps');
      log.info('3. Check logs: docker logs web-analyzer-redis-dev');
      process.exit(1);
    }

  } catch (error) {
    log.error('Redis connection test failed!');
    log.error(error.message);
    console.log();
    log.info('Troubleshooting:');
    log.info('1. Start Redis: npm run redis:start');
    log.info('2. Check Docker is running: docker info');
    log.info('3. Check if port 6379 is available');
    process.exit(1);
  }
}

// Run test
testRedisConnection().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
