#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/turbo@1.13.4/node_modules/turbo/bin/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/turbo@1.13.4/node_modules/turbo/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/turbo@1.13.4/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/turbo@1.13.4/node_modules/turbo/bin/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/turbo@1.13.4/node_modules/turbo/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/turbo@1.13.4/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../turbo/bin/turbo" "$@"
else
  exec node  "$basedir/../turbo/bin/turbo" "$@"
fi
