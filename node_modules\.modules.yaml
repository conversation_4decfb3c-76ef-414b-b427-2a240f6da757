hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': public
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': public
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': public
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': public
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': public
  '@mongodb-js/saslprep@1.3.0':
    '@mongodb-js/saslprep': public
  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-win32-x64': public
  '@puppeteer/browsers@2.3.0':
    '@puppeteer/browsers': public
  '@rollup/rollup-win32-x64-msvc@4.44.0':
    '@rollup/rollup-win32-x64-msvc': public
  '@swc/core-win32-x64-msvc@1.12.6':
    '@swc/core-win32-x64-msvc': public
  '@swc/core@1.12.6':
    '@swc/core': public
  '@swc/counter@0.1.3':
    '@swc/counter': public
  '@swc/types@0.1.23':
    '@swc/types': public
  '@tanstack/history@1.121.34':
    '@tanstack/history': public
  '@tanstack/store@0.7.1':
    '@tanstack/store': public
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': public
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': public
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': public
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': public
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': public
  '@types/body-parser@1.19.6':
    '@types/body-parser': public
  '@types/connect@3.4.38':
    '@types/connect': public
  '@types/cors@2.8.19':
    '@types/cors': public
  '@types/estree@1.0.8':
    '@types/estree': public
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': public
  '@types/express@4.17.23':
    '@types/express': public
  '@types/http-errors@2.0.5':
    '@types/http-errors': public
  '@types/mime@1.3.5':
    '@types/mime': public
  '@types/morgan@1.9.10':
    '@types/morgan': public
  '@types/node@20.19.1':
    '@types/node': public
  '@types/qs@6.14.0':
    '@types/qs': public
  '@types/range-parser@1.2.7':
    '@types/range-parser': public
  '@types/send@0.17.5':
    '@types/send': public
  '@types/serve-static@1.15.8':
    '@types/serve-static': public
  '@types/strip-bom@3.0.0':
    '@types/strip-bom': public
  '@types/strip-json-comments@0.0.30':
    '@types/strip-json-comments': public
  '@types/uuid@9.0.8':
    '@types/uuid': public
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': public
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': public
  '@types/yauzl@2.10.3':
    '@types/yauzl': public
  accepts@1.3.8:
    accepts: public
  acorn-walk@8.3.4:
    acorn-walk: public
  acorn@8.15.0:
    acorn: public
  agent-base@7.1.3:
    agent-base: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  anymatch@3.1.3:
    anymatch: public
  arg@4.1.3:
    arg: public
  argparse@2.0.1:
    argparse: public
  array-flatten@1.1.1:
    array-flatten: public
  ast-types@0.13.4:
    ast-types: public
  b4a@1.6.7:
    b4a: public
  balanced-match@1.0.2:
    balanced-match: public
  bare-events@2.5.4:
    bare-events: public
  bare-fs@4.1.5:
    bare-fs: public
  bare-os@3.6.1:
    bare-os: public
  bare-path@3.0.0:
    bare-path: public
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: public
  base64-js@1.5.1:
    base64-js: public
  basic-auth@2.0.1:
    basic-auth: public
  basic-ftp@5.0.5:
    basic-ftp: public
  binary-extensions@2.3.0:
    binary-extensions: public
  body-parser@1.20.3:
    body-parser: public
  brace-expansion@2.0.2:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  bson@6.10.4:
    bson: public
  buffer-crc32@0.2.13:
    buffer-crc32: public
  buffer-from@1.1.2:
    buffer-from: public
  buffer@5.7.1:
    buffer: public
  bullmq@4.18.2:
    bullmq: public
  bytes@3.1.2:
    bytes: public
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: public
  call-bound@1.0.4:
    call-bound: public
  callsites@3.1.0:
    callsites: public
  chokidar@3.6.0:
    chokidar: public
  chromium-bidi@0.6.3(devtools-protocol@0.0.1312386):
    chromium-bidi: public
  cliui@8.0.1:
    cliui: public
  cluster-key-slot@1.1.2:
    cluster-key-slot: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  concat-map@0.0.1:
    concat-map: public
  content-disposition@0.5.4:
    content-disposition: public
  content-type@1.0.5:
    content-type: public
  cookie-signature@1.0.6:
    cookie-signature: public
  cookie@0.7.1:
    cookie: public
  cors@2.8.5:
    cors: public
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: public
  create-require@1.1.1:
    create-require: public
  cron-parser@4.9.0:
    cron-parser: public
  csstype@3.1.3:
    csstype: public
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: public
  debug@2.6.9:
    debug: public
  degenerator@5.0.1:
    degenerator: public
  denque@2.1.0:
    denque: public
  depd@2.0.0:
    depd: public
  destroy@1.2.0:
    destroy: public
  detect-libc@2.0.4:
    detect-libc: public
  devtools-protocol@0.0.1312386:
    devtools-protocol: public
  diff@4.0.2:
    diff: public
  dotenv@16.5.0:
    dotenv: public
  dunder-proto@1.0.1:
    dunder-proto: public
  dynamic-dedupe@0.3.0:
    dynamic-dedupe: public
  ee-first@1.1.1:
    ee-first: public
  emoji-regex@8.0.0:
    emoji-regex: public
  encodeurl@2.0.0:
    encodeurl: public
  end-of-stream@1.4.5:
    end-of-stream: public
  env-paths@2.2.1:
    env-paths: public
  error-ex@1.3.2:
    error-ex: public
  es-define-property@1.0.1:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-object-atoms@1.1.1:
    es-object-atoms: public
  esbuild@0.25.5:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-html@1.0.3:
    escape-html: public
  escodegen@2.1.0:
    escodegen: public
  esprima@4.0.1:
    esprima: public
  estraverse@5.3.0:
    estraverse: public
  esutils@2.0.3:
    esutils: public
  etag@1.8.1:
    etag: public
  express@4.21.2:
    express: public
  extract-zip@2.0.1:
    extract-zip: public
  fast-fifo@1.3.2:
    fast-fifo: public
  fd-slicer@1.1.0:
    fd-slicer: public
  fdir@6.4.6(picomatch@4.0.2):
    fdir: public
  fill-range@7.1.1:
    fill-range: public
  finalhandler@1.3.1:
    finalhandler: public
  forwarded@0.2.0:
    forwarded: public
  fresh@0.5.2:
    fresh: public
  fs.realpath@1.0.0:
    fs.realpath: public
  function-bind@1.1.2:
    function-bind: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-intrinsic@1.3.0:
    get-intrinsic: public
  get-proto@1.0.1:
    get-proto: public
  get-stream@5.2.0:
    get-stream: public
  get-tsconfig@4.10.1:
    get-tsconfig: public
  get-uri@6.0.4:
    get-uri: public
  glob-parent@5.1.2:
    glob-parent: public
  glob@8.1.0:
    glob: public
  gopd@1.2.0:
    gopd: public
  has-symbols@1.1.0:
    has-symbols: public
  hasown@2.0.2:
    hasown: public
  http-errors@2.0.0:
    http-errors: public
  http-proxy-agent@7.0.2:
    http-proxy-agent: public
  https-proxy-agent@7.0.6:
    https-proxy-agent: public
  iconv-lite@0.4.24:
    iconv-lite: public
  ieee754@1.2.1:
    ieee754: public
  import-fresh@3.3.1:
    import-fresh: public
  inflight@1.0.6:
    inflight: public
  inherits@2.0.4:
    inherits: public
  ioredis@5.6.1:
    ioredis: public
  ip-address@9.0.5:
    ip-address: public
  ipaddr.js@1.9.1:
    ipaddr.js: public
  is-arrayish@0.2.1:
    is-arrayish: public
  is-binary-path@2.1.0:
    is-binary-path: public
  is-core-module@2.16.1:
    is-core-module: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-glob@4.0.3:
    is-glob: public
  is-number@7.0.0:
    is-number: public
  jiti@2.4.2:
    jiti: public
  js-tokens@4.0.0:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsbn@1.1.0:
    jsbn: public
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: public
  kareem@2.6.3:
    kareem: public
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: public
  lightningcss@1.30.1:
    lightningcss: public
  lines-and-columns@1.2.4:
    lines-and-columns: public
  lodash.defaults@4.2.0:
    lodash.defaults: public
  lodash.isarguments@3.1.0:
    lodash.isarguments: public
  lodash@4.17.21:
    lodash: public
  lru-cache@7.18.3:
    lru-cache: public
  luxon@3.6.1:
    luxon: public
  make-error@1.3.6:
    make-error: public
  math-intrinsics@1.1.0:
    math-intrinsics: public
  media-typer@0.3.0:
    media-typer: public
  memory-pager@1.5.0:
    memory-pager: public
  merge-descriptors@1.0.3:
    merge-descriptors: public
  methods@1.1.2:
    methods: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  mime@1.6.0:
    mime: public
  minimatch@5.1.6:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  mitt@3.0.1:
    mitt: public
  mkdirp@1.0.4:
    mkdirp: public
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: public
  mongodb@6.17.0(socks@2.8.5):
    mongodb: public
  morgan@1.10.0:
    morgan: public
  mpath@0.9.0:
    mpath: public
  mquery@5.0.0:
    mquery: public
  ms@2.1.3:
    ms: public
  msgpackr-extract@3.0.3:
    msgpackr-extract: public
  msgpackr@1.11.4:
    msgpackr: public
  nanoid@3.3.11:
    nanoid: public
  negotiator@0.6.3:
    negotiator: public
  netmask@2.0.2:
    netmask: public
  node-abort-controller@3.1.1:
    node-abort-controller: public
  node-gyp-build-optional-packages@5.2.2:
    node-gyp-build-optional-packages: public
  normalize-path@3.0.0:
    normalize-path: public
  object-assign@4.1.1:
    object-assign: public
  object-inspect@1.13.4:
    object-inspect: public
  on-finished@2.4.1:
    on-finished: public
  on-headers@1.0.2:
    on-headers: public
  once@1.4.0:
    once: public
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: public
  pac-resolver@7.0.1:
    pac-resolver: public
  parent-module@1.0.1:
    parent-module: public
  parse-json@5.2.0:
    parse-json: public
  parseurl@1.3.3:
    parseurl: public
  path-is-absolute@1.0.1:
    path-is-absolute: public
  path-parse@1.0.7:
    path-parse: public
  path-to-regexp@0.1.12:
    path-to-regexp: public
  pend@1.2.0:
    pend: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@4.0.2:
    picomatch: public
  postcss@8.5.6:
    postcss: public
  progress@2.0.3:
    progress: public
  proxy-addr@2.0.7:
    proxy-addr: public
  proxy-agent@6.5.0:
    proxy-agent: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  pump@3.0.3:
    pump: public
  punycode@2.3.1:
    punycode: public
  puppeteer-core@22.15.0:
    puppeteer-core: public
  puppeteer@22.15.0(typescript@5.8.3):
    puppeteer: public
  qs@6.13.0:
    qs: public
  range-parser@1.2.1:
    range-parser: public
  raw-body@2.5.2:
    raw-body: public
  readdirp@3.6.0:
    readdirp: public
  redis-errors@1.2.0:
    redis-errors: public
  redis-parser@3.0.0:
    redis-parser: public
  require-directory@2.1.1:
    require-directory: public
  resolve-from@4.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve@1.22.10:
    resolve: public
  rimraf@2.7.1:
    rimraf: public
  rollup@4.44.0:
    rollup: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safer-buffer@2.1.2:
    safer-buffer: public
  semver@7.7.2:
    semver: public
  send@0.19.0:
    send: public
  seroval-plugins@1.3.2(seroval@1.3.2):
    seroval-plugins: public
  seroval@1.3.2:
    seroval: public
  serve-static@1.16.2:
    serve-static: public
  setprototypeof@1.2.0:
    setprototypeof: public
  side-channel-list@1.0.0:
    side-channel-list: public
  side-channel-map@1.0.1:
    side-channel-map: public
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: public
  side-channel@1.1.0:
    side-channel: public
  sift@17.1.3:
    sift: public
  smart-buffer@4.2.0:
    smart-buffer: public
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: public
  socks@2.8.5:
    socks: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map@0.6.1:
    source-map: public
  sparse-bitfield@3.0.3:
    sparse-bitfield: public
  sprintf-js@1.1.3:
    sprintf-js: public
  standard-as-callback@2.1.0:
    standard-as-callback: public
  statuses@2.0.1:
    statuses: public
  streamx@2.22.1:
    streamx: public
  string-width@4.2.3:
    string-width: public
  strip-ansi@6.0.1:
    strip-ansi: public
  strip-bom@3.0.0:
    strip-bom: public
  strip-json-comments@2.0.1:
    strip-json-comments: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  tar-fs@3.0.10:
    tar-fs: public
  tar-stream@3.1.7:
    tar-stream: public
  text-decoder@1.2.3:
    text-decoder: public
  through@2.3.8:
    through: public
  tinyglobby@0.2.14:
    tinyglobby: public
  to-regex-range@5.0.1:
    to-regex-range: public
  toidentifier@1.0.1:
    toidentifier: public
  tr46@5.1.1:
    tr46: public
  tree-kill@1.2.2:
    tree-kill: public
  ts-node-dev@2.0.0(@swc/core@1.12.6)(@types/node@20.19.1)(typescript@5.8.3):
    ts-node-dev: public
  ts-node@10.9.2(@swc/core@1.12.6)(@types/node@20.19.1)(typescript@5.8.3):
    ts-node: public
  tsconfig@7.0.0:
    tsconfig: public
  tslib@2.8.1:
    tslib: public
  tsx@4.20.3:
    tsx: public
  turbo-windows-64@1.13.4:
    turbo-windows-64: public
  type-is@1.6.18:
    type-is: public
  typescript@5.8.3:
    typescript: public
  unbzip2-stream@1.4.3:
    unbzip2-stream: public
  undici-types@6.21.0:
    undici-types: public
  unpipe@1.0.0:
    unpipe: public
  urlpattern-polyfill@10.0.0:
    urlpattern-polyfill: public
  utils-merge@1.0.1:
    utils-merge: public
  uuid@9.0.1:
    uuid: public
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: public
  vary@1.1.2:
    vary: public
  webidl-conversions@7.0.0:
    webidl-conversions: public
  whatwg-url@14.2.0:
    whatwg-url: public
  wrap-ansi@7.0.0:
    wrap-ansi: public
  wrappy@1.0.2:
    wrappy: public
  ws@8.18.2:
    ws: public
  xtend@4.0.2:
    xtend: public
  y18n@5.0.8:
    y18n: public
  yargs-parser@21.1.1:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yauzl@2.10.0:
    yauzl: public
  yn@3.1.1:
    yn: public
  zod@3.23.8:
    zod: public
ignoredBuilds:
  - puppeteer
  - msgpackr-extract
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.3
pendingBuilds: []
prunedAt: Thu, 26 Jun 2025 05:26:08 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - '@swc/core-darwin-arm64@1.12.6'
  - '@swc/core-darwin-x64@1.12.6'
  - '@swc/core-linux-arm-gnueabihf@1.12.6'
  - '@swc/core-linux-arm64-gnu@1.12.6'
  - '@swc/core-linux-arm64-musl@1.12.6'
  - '@swc/core-linux-x64-gnu@1.12.6'
  - '@swc/core-linux-x64-musl@1.12.6'
  - '@swc/core-win32-arm64-msvc@1.12.6'
  - '@swc/core-win32-ia32-msvc@1.12.6'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - turbo-darwin-64@1.13.4
  - turbo-darwin-arm64@1.13.4
  - turbo-linux-64@1.13.4
  - turbo-linux-arm64@1.13.4
  - turbo-windows-arm64@1.13.4
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\AI-web-scrapping\node_modules\.pnpm
virtualStoreDirMaxLength: 60
