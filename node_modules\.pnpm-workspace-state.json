{"lastValidatedTimestamp": 1750915569076, "projects": {"C:\\Users\\<USER>\\Desktop\\AI-web-scrapping": {"name": "web-analyzer-platform", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\AI-web-scrapping\\apps\\api-server": {"name": "api-server", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\AI-web-scrapping\\apps\\scraper-engine": {"name": "scraper-engine", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\AI-web-scrapping\\packages\\config": {"name": "@web-analyzer/config", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\AI-web-scrapping\\packages\\db-models": {"name": "@web-analyzer/db-models", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\AI-web-scrapping\\packages\\shared-logic": {"name": "@web-analyzer/shared-logic", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": ["*"], "workspacePackagePatterns": ["apps/*", "packages/*"]}, "filteredInstall": false}