#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/browsers/lib/cjs/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/browsers/lib/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/browsers/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/browsers/lib/cjs/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/browsers/lib/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/browsers/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules/@puppeteer/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/@puppeteer+browsers@2.3.0/node_modules:/mnt/c/Users/<USER>/Desktop/AI-web-scrapping/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../lib/cjs/main-cli.js" "$@"
else
  exec node  "$basedir/../../lib/cjs/main-cli.js" "$@"
fi
