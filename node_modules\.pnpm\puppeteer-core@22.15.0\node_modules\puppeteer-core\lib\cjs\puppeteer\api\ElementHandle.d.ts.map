{"version": 3, "file": "ElementHandle.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/ElementHandle.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AAG3C,OAAO,KAAK,EACV,iBAAiB,EACjB,UAAU,EACV,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,OAAO,EACR,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,+BAA+B,CAAC;AAM5D,OAAO,EAAC,gBAAgB,EAAC,MAAM,0BAA0B,CAAC;AAC1D,OAAO,KAAK,EACV,mBAAmB,EACnB,eAAe,EACf,iBAAiB,EAClB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAC,QAAQ,EAAC,MAAM,eAAe,CAAC;AACvC,OAAO,KAAK,EACV,YAAY,EACZ,iBAAiB,EACjB,sBAAsB,EACvB,MAAM,WAAW,CAAC;AAEnB;;GAEG;AACH,MAAM,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAEhD;;GAEG;AACH,MAAM,WAAW,QAAQ;IACvB,OAAO,EAAE,IAAI,CAAC;IACd,OAAO,EAAE,IAAI,CAAC;IACd,MAAM,EAAE,IAAI,CAAC;IACb,MAAM,EAAE,IAAI,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,WAAY,SAAQ,KAAK;IACxC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,MAAM;IACrB;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,iBAAiB;IACrD;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,KAAK;IACpB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,iBAAiB;IACjE;;OAEG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;CAC1B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,8BAAsB,aAAa,CACjC,WAAW,SAAS,IAAI,GAAG,OAAO,CAClC,SAAQ,QAAQ,CAAC,WAAW,CAAC;;IAC7B;;OAEG;IACK,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAEpC;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,IAAI,CAAC;IAE7B;;;;;;;OAOG;IACH,MAAM,CAAC,kBAAkB,CAAC,IAAI,SAAS,aAAa,CAAC,IAAI,CAAC,EACxD,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,EACpD,CAAC,EAAE,OAAO,GACT,OAAO,MAAM;IAgDhB;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,MAAM,wBAAC;IAE1B;;OAEG;gBACS,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC;IAMzC;;OAEG;IACH,IAAa,EAAE,IAAI,MAAM,GAAG,SAAS,CAEpC;IAED;;OAEG;IACH,IAAa,QAAQ,IAAI,OAAO,CAE/B;IAED;;OAEG;IAGY,WAAW,CAAC,CAAC,SAAS,MAAM,WAAW,EACpD,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC,GACxB,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAIrC;;OAEG;IAGY,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAI9D;;OAEG;IACY,QAAQ,CACrB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,GAAG,gBAAgB,CACnE,WAAW,EACX,MAAM,CACP,EAED,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAQrC;;OAEG;IACY,cAAc,CAC3B,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,GAAG,gBAAgB,CACnE,WAAW,EACX,MAAM,CACP,EAED,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAQhD;;OAEG;IAGY,SAAS,IAAI,OAAO,CAAC,WAAW,CAAC;IAIhD;;OAEG;IACM,QAAQ,IAAI,MAAM;IAI3B;;OAEG;IACM,YAAY,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY;IAItD;;OAEG;IACM,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC;;OAEG;IACM,SAAS,IAAI,aAAa,CAAC,WAAW,CAAC;IAIhD;;OAEG;IACH,QAAQ,KAAK,KAAK,IAAI,KAAK,CAAC;IAE5B;;;;;;;;;;;;;;;;;;;;OAoBG;IAGG,CAAC,CAAC,QAAQ,SAAS,MAAM,EAC7B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IASnD;;;;;;;;;;;;;;;;;;;;OAoBG;IAEG,EAAE,CAAC,QAAQ,SAAS,MAAM,EAC9B,QAAQ,EAAE,QAAQ,EAClB,OAAO,CAAC,EAAE,YAAY,GACrB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAkCnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACG,KAAK,CACT,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,GAAG,gBAAgB,CACzE,OAAO,CAAC,QAAQ,CAAC,EACjB,MAAM,CACP,EAED,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAWrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8CG;IACG,MAAM,CACV,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAC3B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EACxB,MAAM,CACP,GAAG,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAEtD,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAkBrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IAGG,eAAe,CAAC,QAAQ,SAAS,MAAM,EAC3C,QAAQ,EAAE,QAAQ,EAClB,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAqBnD;;;;;;;;;;;;OAYG;IAGG,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC;IAInC;;;;;;;;;;;OAWG;IAGG,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;IAIlC;;;;;;;;;;;;;;;;;OAiBG;IAGG,SAAS,CACb,CAAC,SAAS,MAAM,qBAAqB,GAAG,MAAM,oBAAoB,EAClE,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAUhD;;;OAGG;IACH,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7E,QAAQ,CAAC,YAAY,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IAE9C;;OAEG;IAGG,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;IAiBrD;;;;OAIG;IAGG,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAMxD;;;;OAIG;IAGG,KAAK,CACT,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,OAAO,GAAE,QAAQ,CAAC,YAAY,CAAM,GACnC,OAAO,CAAC,IAAI,CAAC;IAMhB;;;;;OAKG;IAGG,IAAI,CACR,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,MAAM,EAAE,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,GACrC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;IA2B1C;;OAEG;IAGG,SAAS,CACb,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAOhB;;OAEG;IAGG,QAAQ,CACZ,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAOhB;;OAEG;IACG,IAAI,CACR,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,GAC9B,OAAO,CAAC,IAAI,CAAC;IAEhB;;OAEG;IACG,IAAI,CACR,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,IAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAC7B,OAAO,CAAC,IAAI,CAAC;IA4BhB;;OAEG;IAGG,WAAW,CACf,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,EAC3B,OAAO,CAAC,EAAE;QAAC,KAAK,EAAE,MAAM,CAAA;KAAC,GACxB,OAAO,CAAC,IAAI,CAAC;IAYhB;;;;;;;;;;;;;;;OAeG;IAGG,MAAM,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IA4CpD;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,UAAU,CACjB,IAAI,EAAE,aAAa,CAAC,gBAAgB,CAAC,EACrC,GAAG,KAAK,EAAE,MAAM,EAAE,GACjB,OAAO,CAAC,IAAI,CAAC;IAEhB;;OAEG;IACH,QAAQ,CAAC,WAAW,CAClB,IAAI,CAAC,EAAE,MAAM,EACb,IAAI,CAAC,EAAE,MAAM,GACZ,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAEzC;;;;OAIG;IAGG,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAQhD,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAQvD,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAQtD,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAK3D;;OAEG;IAGG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAS5B;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IAGG,IAAI,CACR,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,GACtC,OAAO,CAAC,IAAI,CAAC;IAKhB;;;;;;;;;;;;;OAaG;IAGG,KAAK,CACT,GAAG,EAAE,QAAQ,EACb,OAAO,CAAC,EAAE,QAAQ,CAAC,eAAe,CAAC,GAClC,OAAO,CAAC,IAAI,CAAC;IAiFhB;;;;OAIG;IAGG,WAAW,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IA2BhD;;;;;;;;;OASG;IAGG,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAmI1C;;;;OAIG;IACG,UAAU,CACd,OAAO,EAAE,QAAQ,CAAC,iBAAiB,CAAC,GAAG;QAAC,QAAQ,EAAE,QAAQ,CAAA;KAAC,GAC1D,OAAO,CAAC,MAAM,CAAC;IACZ,UAAU,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IA8CxE;;OAEG;cACa,sBAAsB,IAAI,OAAO,CAAC,IAAI,CAAC;IAgBvD;;OAEG;cACa,sBAAsB,CACpC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAC3B,OAAO,CAAC,IAAI,CAAC;IAWhB;;;;;;;OAOG;IAGG,sBAAsB,CAC1B,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,OAAO,GAAE;QACP,SAAS,CAAC,EAAE,MAAM,CAAC;KACf,GACL,OAAO,CAAC,OAAO,CAAC;IAoBnB;;;OAGG;IAGG,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAyCjE;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;CACrD;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,UAAU,EAAE;QAEV,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE,MAAM,CAAC;QACnB,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;CACH"}