{"version": 3, "file": "Serializer.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Serializer.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH,+CAAkE;AAElE;;GAEG;AACH,MAAM,mBAAoB,SAAQ,KAAK;CAAG;AAE1C;;GAEG;AACH,MAAa,cAAc;IACzB,MAAM,CAAC,SAAS,CAAC,GAAY;QAC3B,QAAQ,OAAO,GAAG,EAAE,CAAC;YACnB,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU;gBACb,MAAM,IAAI,mBAAmB,CAAC,0BAA0B,OAAO,GAAG,EAAE,CAAC,CAAC;YACxE,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAEpC,KAAK,WAAW;gBACd,OAAO;oBACL,IAAI,EAAE,WAAW;iBAClB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACpC,KAAK,QAAQ;gBACX,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE;iBACtB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG;iBACX,CAAC;YACJ,KAAK,SAAS;gBACZ,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,GAAG;iBACX,CAAC;QACN,CAAC;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,GAAW;QACjC,IAAI,KAAyC,CAAC;QAC9C,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,GAAG,IAAI,CAAC;QACf,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC;YACpC,KAAK,GAAG,UAAU,CAAC;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,KAAK,GAAG,WAAW,CAAC;QACtB,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,KAAK,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,GAAG,CAAC;QACd,CAAC;QACD,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,GAAkB;QACxC,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,OAAO;gBACL,IAAI,EAAE,MAAM;aACb,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,WAAW;aACnB,CAAC;QACJ,CAAC;aAAM,IAAI,IAAA,uBAAa,EAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IACE,KAAK,YAAY,SAAS;oBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE,CAAC;oBACD,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;gBACzD,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,YAAY,GAAkC,EAAE,CAAC;YACvD,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACtB,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;aAAM,IAAI,IAAA,kBAAQ,EAAC,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,GAAG,CAAC,MAAM;oBACnB,KAAK,EAAE,GAAG,CAAC,KAAK;iBACjB;aACF,CAAC;QACJ,CAAC;aAAM,IAAI,IAAA,gBAAM,EAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO;gBACL,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE;aACzB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,mBAAmB,CAC3B,sEAAsE,CACvE,CAAC;IACJ,CAAC;CACF;AA3GD,wCA2GC"}