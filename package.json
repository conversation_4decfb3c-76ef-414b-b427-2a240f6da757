{"name": "web-analyzer-platform", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "devDependencies": {"@types/mongoose": "^5.11.97", "turbo": "^1.10.12"}, "dependencies": {"@tanstack/router-core": "^1.121.34", "mongoose": "^8.16.0", "solid-js": "^1.9.7", "tiny-invariant": "^1.3.3", "vite": "^6.3.5"}}