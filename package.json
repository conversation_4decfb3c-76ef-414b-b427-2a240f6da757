{"name": "web-analyzer-platform", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "node scripts/dev-start.js", "dev:turbo": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "redis:start": "node scripts/start-redis.js", "docker:up": "docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d", "docker:down": "docker-compose -f docker-compose.dev.yml down", "docker:build": "docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d --build", "docker:logs": "docker-compose -f docker-compose.dev.yml logs -f", "docker:test": "node scripts/test-docker.js", "docker:clean": "docker-compose -f docker-compose.dev.yml down -v --remove-orphans && docker system prune -f", "start": "npm run docker:build", "stop": "npm run docker:down", "restart": "npm run docker:down && npm run docker:up", "setup": "node scripts/setup-project.js"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "devDependencies": {"@types/mongoose": "^5.11.97", "turbo": "^1.10.12"}, "dependencies": {"@tanstack/router-core": "^1.121.34", "mongoose": "^8.16.0", "solid-js": "^1.9.7", "tiny-invariant": "^1.3.3", "vite": "^6.3.5"}}