export declare const config: {
    apiServerPort: string | number;
    scraperEnginePort: string | number;
    frontendPort: string | number;
    adminPortalPort: string | number;
    nodeEnv: string;
    mongoUri: string;
    mongoDBName: string;
    jwtAccessTokenSecret: string;
    jwtRefreshTokenSecret: string;
    jwtAccessTokenExpiration: string;
    jwtRefreshTokenExpiration: string;
    geminiApiKey: string;
    chatGPTApiKey: string;
    apiServerUrl: string;
    scraperEngineUrl: string;
    frontendUrl: string;
    adminPortalUrl: string;
    redisUrl: string;
    redisPassword: string;
    redisUsername: string;
    redisDB: string | number;
    redisMaxRetriesPerRequest: number;
    redisEnableOfflineQueue: boolean;
    redisConnectTimeout: number;
    redisRetryStrategy: number;
    redisMaxLoadingRetryTime: number;
    redisKeyPrefix: string;
    bullMQRedis: {
        port: number;
        host: string;
        password: string;
        username: string;
        db: number;
        maxRetriesPerRequest: number;
        enableOfflineQueue: boolean;
        connectTimeout: number;
        retryStrategy: number;
        maxLoadingRetryTime: number;
    };
};
//# sourceMappingURL=index.d.ts.map