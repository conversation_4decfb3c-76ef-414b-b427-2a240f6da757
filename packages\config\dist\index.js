"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
// Shared configuration for all services
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
exports.config = {
    // Ports
    apiServerPort: process.env.API_SERVER_PORT || 5000,
    scraperEnginePort: process.env.SCRAPER_ENGINE_PORT || 5001,
    frontendPort: process.env.FRONTEND_PORT || 3000,
    adminPortalPort: process.env.ADMIN_PORTAL_PORT || 3001,
    // Environment
    nodeEnv: process.env.NODE_ENV || "development",
    // Mongo
    mongoUri: process.env.MONGO_URI || "mongodb://localhost:27017/web-analyzer",
    mongoDBName: process.env.MONGO_DB_NAME || "web-analyzer",
    // Auth
    jwtAccessTokenSecret: process.env.JWT_ACCESS_TOKEN_SECRET || "super-secret",
    jwtRefreshTokenSecret: process.env.JWT_REFRESH_TOKEN_SECRET || "super-secret",
    jwtAccessTokenExpiration: process.env.JWT_ACCESS_TOKEN_EXPIRATION || "1h",
    jwtRefreshTokenExpiration: process.env.JWT_REFRESH_TOKEN_EXPIRATION || "15d",
    // AI
    geminiApiKey: process.env.GEMINI_API_KEY || "",
    chatGPTApiKey: process.env.CHAT_GPT_API_KEY || "",
    // Service URL
    apiServerUrl: process.env.API_SERVER_URL || "http://localhost:5000",
    scraperEngineUrl: process.env.SCRAPER_ENGINE_URL || "http://localhost:5001",
    frontendUrl: process.env.FRONTEND_URL || "http://localhost:3000",
    adminPortalUrl: process.env.ADMIN_PORTAL_URL || "http://localhost:3001",
    //   // Feature Flags store and read form the database
    //   isAIEnabled: process.env.IS_AI_ENABLED || false,
    //   isScraperEnabled: process.env.IS_SCRAPER_ENABLED || false,
    //   isAuditEnabled: process.env.IS_AUDIT_ENABLED || false,
    //   isReportGeneratorEnabled: process.env.IS_REPORT_GENERATOR_ENABLED || false,
    //   isAuthEnabled: process.env.IS_AUTH_ENABLED || false,
    //   isWebhookEnabled: process.env.IS_WEBHOOK_ENABLED || false,
    //   isQueueEnabled: process.env.IS_QUEUE_ENABLED || false,
    //   isCacheEnabled: process.env.IS_CACHE_ENABLED || false,
    //   isRateLimiterEnabled: process.env.IS_RATE_LIMITER_ENABLED || false,
    //   isLoggingEnabled: process.env.IS_LOGGING_ENABLED || false,
    //   isMonitoringEnabled: process.env.IS_MONITORING_ENABLED || false,
    //   isCdnEnabled: process.env.IS_CDN_ENABLED || false,
    //   isSearchEnabled: process.env.IS_SEARCH_ENABLED || false,
    //   isBackupEnabled: process.env.IS_BACKUP_ENABLED || false,
    //   isLoadTestingEnabled: process.env.IS_LOAD_TESTING_ENABLED || false,
    //   isCanaryTestingEnabled: process.env.IS_CANARY_TESTING_ENABLED || false,
    //   isFeatureFlagEnabled: process.env.IS_FEATURE_FLAG_ENABLED || false,
    // Rate Limiting (Wi will read form the database)
    //   maxRequestsPerMinute: process.env.MAX_REQUESTS_PER_MINUTE || 1000,
    //   maxConcurrentRequests: process.env.MAX_CONCURRENT_REQUESTS || 100,
    // Redis
    redisUrl: process.env.REDIS_URL || "redis://localhost:6379",
    redisPassword: process.env.REDIS_PASSWORD || "",
    redisUsername: process.env.REDIS_USERNAME || "",
    redisDB: process.env.REDIS_DB || 0,
    redisMaxRetriesPerRequest: 20,
    redisEnableOfflineQueue: true,
    redisConnectTimeout: 10000,
    redisRetryStrategy: 1000,
    redisMaxLoadingRetryTime: 10000,
    redisKeyPrefix: "web-analyzer:",
    // BullMQ
    bullMQRedis: {
        port: 6379,
        host: "localhost",
        password: "",
        username: "",
        db: 0,
        maxRetriesPerRequest: 20,
        enableOfflineQueue: true,
        connectTimeout: 10000,
        retryStrategy: 1000,
        maxLoadingRetryTime: 10000,
    },
};
