import mongoose from "mongoose";
import { config } from "@web-analyzer/config";

const connectDB = async (DATABASE_URL?: string) => {
  try {
    const mongoUri = DATABASE_URL || config.mongoUri;
    await mongoose.connect(mongoUri, {
      dbName: config.mongoDBName,
    });
    console.log("✅ Connected to MongoDB");
    return true;
  } catch (error: any) {
    console.log("❌ MongoDB connection error:", error.message || error);
    console.log("⚠️  Continuing without database connection...");
    return false;
  }
};

export { connectDB };
export default connectDB;
