"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectDB = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const config_1 = require("@web-analyzer/config");
const connectDB = async (DATABASE_URL) => {
    try {
        const mongoUri = DATABASE_URL || config_1.config.mongoUri;
        await mongoose_1.default.connect(mongoUri, {
            dbName: config_1.config.mongoDBName,
        });
        console.log("✅ Connected to MongoDB");
        return true;
    }
    catch (error) {
        console.log("❌ MongoDB connection error:", error.message || error);
        console.log("⚠️  Continuing without database connection...");
        return false;
    }
};
exports.connectDB = connectDB;
exports.default = connectDB;
