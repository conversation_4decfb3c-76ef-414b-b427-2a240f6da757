"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrapeJob = exports.User = exports.default = exports.connectDB = void 0;
var connection_1 = require("./connection");
Object.defineProperty(exports, "connectDB", { enumerable: true, get: function () { return connection_1.connectDB; } });
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return connection_1.connectDB; } });
var User_1 = require("./models/User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
var ScrapeJob_1 = require("./models/ScrapeJob");
Object.defineProperty(exports, "ScrapeJob", { enumerable: true, get: function () { return ScrapeJob_1.ScrapeJob; } });
