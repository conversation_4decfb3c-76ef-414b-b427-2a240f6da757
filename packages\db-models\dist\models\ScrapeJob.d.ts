import mongoose, { Document } from 'mongoose';
export interface IScrapeJob extends Document {
    url: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    userId: mongoose.Types.ObjectId;
    result?: any;
    error?: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const ScrapeJob: mongoose.Model<IScrapeJob, {}, {}, {}, mongoose.Document<unknown, {}, IScrapeJob, {}> & IScrapeJob & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=ScrapeJob.d.ts.map