import mongoose, { Document, Schema } from 'mongoose';

export interface IScrapeJob extends Document {
  url: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  userId: mongoose.Types.ObjectId;
  queueJobId?: string;
  result?: any;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ScrapeJobSchema: Schema = new Schema({
  url: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: ['pending', 'running', 'completed', 'failed'],
    default: 'pending'
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  queueJobId: {
    type: String,
    default: null
  },
  result: {
    type: Schema.Types.Mixed,
    default: null
  },
  error: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

export const ScrapeJob = mongoose.model<IScrapeJob>('ScrapeJob', ScrapeJobSchema);
