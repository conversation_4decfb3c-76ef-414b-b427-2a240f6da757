
> @web-analyzer/shared-logic@1.0.0 build C:\Users\<USER>\Desktop\AI-web-scrapping\packages\shared-logic
> tsc --declaration --emitDeclarationOnly false --outDir dist

error TS5055: Cannot write file 'C:/Users/<USER>/Desktop/AI-web-scrapping/packages/shared-logic/dist/packages/shared-logic/index.d.ts' because it would overwrite input file.
../../tsconfig.json(31,5): error TS6053: File 'C:/Users/<USER>/Desktop/AI-web-scrapping/packages/shared-logic' not found.
 ELIFECYCLE  Command failed with exit code 1.
 WARN   Local package.json exists, but node_modules missing, did you mean to install?
