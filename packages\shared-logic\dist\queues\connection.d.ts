import { Queue, QueueEvents } from 'bullmq';
import { ScrapeJobData } from './types';
export declare const redisConnection: {
    host: string;
    port: number;
    password: string | undefined;
    username: string | undefined;
    db: number;
    maxRetriesPerRequest: number;
    enableOfflineQueue: boolean;
    connectTimeout: number;
    retryDelayOnFailover: number;
    maxLoadingTimeout: number;
};
export declare const scrapeJobsQueue: Queue<ScrapeJobData, any, string>;
export declare const scrapeJobsQueueEvents: QueueEvents;
export declare const addScrapeJob: (jobData: ScrapeJobData, options?: {
    delay?: number;
    priority?: number;
}) => Promise<import("bullmq").Job<ScrapeJobData, any, string>>;
export declare const getJobStatus: (jobId: string) => Promise<{
    id: string | undefined;
    name: string;
    data: ScrapeJobData;
    progress: number | object;
    returnvalue: any;
    failedReason: string;
    processedOn: number | undefined;
    finishedOn: number | undefined;
    opts: import("bullmq").JobsOptions;
} | null>;
export declare const getQueueStats: () => Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    total: number;
}>;
export declare const closeQueues: () => Promise<void>;
//# sourceMappingURL=connection.d.ts.map