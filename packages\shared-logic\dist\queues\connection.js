"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeQueues = exports.getQueueStats = exports.getJobStatus = exports.addScrapeJob = exports.scrapeJobsQueueEvents = exports.scrapeJobsQueue = exports.redisConnection = void 0;
const bullmq_1 = require("bullmq");
const config_1 = require("@web-analyzer/config");
const types_1 = require("./types");
// Redis connection configuration for BullMQ
exports.redisConnection = {
    host: config_1.config.bullMQRedis.host,
    port: config_1.config.bullMQRedis.port,
    password: config_1.config.bullMQRedis.password || undefined,
    username: config_1.config.bullMQRedis.username || undefined,
    db: config_1.config.bullMQRedis.db,
    maxRetriesPerRequest: config_1.config.bullMQRedis.maxRetriesPerRequest,
    enableOfflineQueue: config_1.config.bullMQRedis.enableOfflineQueue,
    connectTimeout: config_1.config.bullMQRedis.connectTimeout,
    retryDelayOnFailover: config_1.config.bullMQRedis.retryStrategy,
    maxLoadingTimeout: config_1.config.bullMQRedis.maxLoadingRetryTime,
};
// Create queues
exports.scrapeJobsQueue = new bullmq_1.Queue(types_1.QueueNames.SCRAPE_JOBS, {
    connection: exports.redisConnection,
    defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: 3, // Retry failed jobs 3 times
        backoff: {
            type: 'exponential',
            delay: 2000,
        },
    },
});
// Queue events for monitoring
exports.scrapeJobsQueueEvents = new bullmq_1.QueueEvents(types_1.QueueNames.SCRAPE_JOBS, {
    connection: exports.redisConnection,
});
// Helper function to add a scrape job
const addScrapeJob = async (jobData, options) => {
    const job = await exports.scrapeJobsQueue.add('scrape-website', jobData, {
        delay: options?.delay,
        priority: options?.priority,
    });
    console.log(`📋 Added scrape job ${job.id} for URL: ${jobData.url}`);
    return job;
};
exports.addScrapeJob = addScrapeJob;
// Helper function to get job status
const getJobStatus = async (jobId) => {
    const job = await exports.scrapeJobsQueue.getJob(jobId);
    if (!job) {
        return null;
    }
    return {
        id: job.id,
        name: job.name,
        data: job.data,
        progress: job.progress,
        returnvalue: job.returnvalue,
        failedReason: job.failedReason,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
        opts: job.opts,
    };
};
exports.getJobStatus = getJobStatus;
// Helper function to get queue stats
const getQueueStats = async () => {
    const waiting = await exports.scrapeJobsQueue.getWaiting();
    const active = await exports.scrapeJobsQueue.getActive();
    const completed = await exports.scrapeJobsQueue.getCompleted();
    const failed = await exports.scrapeJobsQueue.getFailed();
    return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length + completed.length + failed.length,
    };
};
exports.getQueueStats = getQueueStats;
// Graceful shutdown
const closeQueues = async () => {
    console.log('🔄 Closing queue connections...');
    await exports.scrapeJobsQueue.close();
    await exports.scrapeJobsQueueEvents.close();
    console.log('✅ Queue connections closed');
};
exports.closeQueues = closeQueues;
