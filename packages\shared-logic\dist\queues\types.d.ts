export interface ScrapeJobData {
    url: string;
    userId: string;
    jobId: string;
    options?: {
        waitForSelector?: string;
        timeout?: number;
        screenshot?: boolean;
        fullPage?: boolean;
        extractText?: boolean;
        extractLinks?: boolean;
        extractImages?: boolean;
        customSelectors?: Record<string, string>;
    };
}
export interface ScrapeJobResult {
    jobId: string;
    url: string;
    status: 'completed' | 'failed';
    data?: {
        title?: string;
        content?: string;
        links?: string[];
        images?: string[];
        screenshot?: string;
        customData?: Record<string, any>;
        metadata?: {
            loadTime: number;
            timestamp: string;
            userAgent: string;
        };
    };
    error?: string;
    completedAt: string;
}
export interface JobProgress {
    jobId: string;
    progress: number;
    message: string;
    timestamp: string;
}
export declare enum QueueNames {
    SCRAPE_JOBS = "scrape-jobs",
    NOTIFICATIONS = "notifications",
    CLEANUP = "cleanup"
}
export declare enum JobStatus {
    PENDING = "pending",
    ACTIVE = "active",
    COMPLETED = "completed",
    FAILED = "failed",
    DELAYED = "delayed",
    WAITING = "waiting"
}
export interface QueueJobOptions {
    delay?: number;
    attempts?: number;
    backoff?: {
        type: 'fixed' | 'exponential';
        delay: number;
    };
    removeOnComplete?: number;
    removeOnFail?: number;
}
//# sourceMappingURL=types.d.ts.map