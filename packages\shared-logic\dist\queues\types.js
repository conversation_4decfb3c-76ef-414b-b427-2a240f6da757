"use strict";
// Job types and interfaces for the queue system
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobStatus = exports.QueueNames = void 0;
var QueueNames;
(function (QueueNames) {
    QueueNames["SCRAPE_JOBS"] = "scrape-jobs";
    QueueNames["NOTIFICATIONS"] = "notifications";
    QueueNames["CLEANUP"] = "cleanup";
})(QueueNames || (exports.QueueNames = QueueNames = {}));
var JobStatus;
(function (JobStatus) {
    JobStatus["PENDING"] = "pending";
    JobStatus["ACTIVE"] = "active";
    JobStatus["COMPLETED"] = "completed";
    JobStatus["FAILED"] = "failed";
    JobStatus["DELAYED"] = "delayed";
    JobStatus["WAITING"] = "waiting";
})(JobStatus || (exports.JobStatus = JobStatus = {}));
