export declare class JobStatusService {
    private static instance;
    private statusListeners;
    private constructor();
    static getInstance(): JobStatusService;
    private setupQueueEventListeners;
    private updateJobStatusByQueueId;
    updateJobStatus(jobId: string, status: 'pending' | 'running' | 'completed' | 'failed', result?: any, error?: string): Promise<(import("mongoose").Document<unknown, {}, import("@web-analyzer/db-models").IScrapeJob, {}> & import("@web-analyzer/db-models").IScrapeJob & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }) | null>;
    getJobStatus(jobId: string): Promise<(import("mongoose").Document<unknown, {}, import("@web-analyzer/db-models").IScrapeJob, {}> & import("@web-analyzer/db-models").IScrapeJob & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }) | null>;
    getJobsByUser(userId: string, options?: {
        status?: string;
        limit?: number;
        offset?: number;
    }): Promise<{
        jobs: (import("mongoose").Document<unknown, {}, import("@web-analyzer/db-models").IScrapeJob, {}> & import("@web-analyzer/db-models").IScrapeJob & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        total: number;
        hasMore: boolean;
    }>;
    subscribeToJobStatus(jobId: string, callback: (status: any) => void): void;
    unsubscribeFromJobStatus(jobId: string): void;
    private notifyStatusListeners;
    getJobStatistics(): Promise<{
        pending: number;
        running: number;
        completed: number;
        failed: number;
        total: number;
    }>;
}
export declare const jobStatusService: JobStatusService;
//# sourceMappingURL=jobStatusService.d.ts.map