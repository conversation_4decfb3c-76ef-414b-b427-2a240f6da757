"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.jobStatusService = exports.JobStatusService = void 0;
const db_models_1 = require("@web-analyzer/db-models");
const connection_1 = require("../queues/connection");
class JobStatusService {
    constructor() {
        this.statusListeners = new Map();
        this.setupQueueEventListeners();
    }
    static getInstance() {
        if (!JobStatusService.instance) {
            JobStatusService.instance = new JobStatusService();
        }
        return JobStatusService.instance;
    }
    setupQueueEventListeners() {
        // Listen to queue events and update database accordingly
        connection_1.scrapeJobsQueueEvents.on('waiting', async ({ jobId }) => {
            console.log(`📋 Job ${jobId} is waiting`);
            await this.updateJobStatusByQueueId(jobId, 'pending');
        });
        connection_1.scrapeJobsQueueEvents.on('active', async ({ jobId }) => {
            console.log(`⚡ Job ${jobId} is active`);
            await this.updateJobStatusByQueueId(jobId, 'running');
        });
        connection_1.scrapeJobsQueueEvents.on('completed', async ({ jobId, returnvalue }) => {
            console.log(`✅ Job ${jobId} completed`);
            await this.updateJobStatusByQueueId(jobId, 'completed', returnvalue);
        });
        connection_1.scrapeJobsQueueEvents.on('failed', async ({ jobId, failedReason }) => {
            console.log(`❌ Job ${jobId} failed: ${failedReason}`);
            await this.updateJobStatusByQueueId(jobId, 'failed', null, failedReason);
        });
        connection_1.scrapeJobsQueueEvents.on('progress', async ({ jobId, data }) => {
            console.log(`📊 Job ${jobId} progress: ${data}%`);
            // Optionally store progress in database or emit to real-time listeners
            this.notifyStatusListeners(jobId, { progress: data });
        });
    }
    async updateJobStatusByQueueId(queueJobId, status, result, error) {
        try {
            const updateData = { status };
            if (result)
                updateData.result = result;
            if (error)
                updateData.error = error;
            const job = await db_models_1.ScrapeJob.findOneAndUpdate({ queueJobId }, updateData, { new: true });
            if (job) {
                this.notifyStatusListeners(job._id?.toString() || '', {
                    status,
                    result,
                    error,
                    updatedAt: new Date().toISOString()
                });
            }
        }
        catch (dbError) {
            console.error('Failed to update job status in database:', dbError);
        }
    }
    async updateJobStatus(jobId, status, result, error) {
        try {
            const updateData = { status };
            if (result)
                updateData.result = result;
            if (error)
                updateData.error = error;
            const job = await db_models_1.ScrapeJob.findByIdAndUpdate(jobId, updateData, { new: true });
            if (job) {
                this.notifyStatusListeners(jobId, {
                    status,
                    result,
                    error,
                    updatedAt: new Date().toISOString()
                });
            }
            return job;
        }
        catch (dbError) {
            console.error('Failed to update job status:', dbError);
            throw dbError;
        }
    }
    async getJobStatus(jobId) {
        try {
            const job = await db_models_1.ScrapeJob.findById(jobId).populate('userId', 'name email');
            return job;
        }
        catch (error) {
            console.error('Failed to get job status:', error);
            throw error;
        }
    }
    async getJobsByUser(userId, options = {}) {
        try {
            const { status, limit = 50, offset = 0 } = options;
            const filter = { userId };
            if (status)
                filter.status = status;
            const jobs = await db_models_1.ScrapeJob.find(filter)
                .sort({ createdAt: -1 })
                .limit(limit)
                .skip(offset);
            const total = await db_models_1.ScrapeJob.countDocuments(filter);
            return {
                jobs,
                total,
                hasMore: total > offset + limit
            };
        }
        catch (error) {
            console.error('Failed to get jobs by user:', error);
            throw error;
        }
    }
    subscribeToJobStatus(jobId, callback) {
        this.statusListeners.set(jobId, callback);
    }
    unsubscribeFromJobStatus(jobId) {
        this.statusListeners.delete(jobId);
    }
    notifyStatusListeners(jobId, status) {
        const listener = this.statusListeners.get(jobId);
        if (listener) {
            listener(status);
        }
    }
    async getJobStatistics() {
        try {
            const stats = await db_models_1.ScrapeJob.aggregate([
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ]);
            const result = {
                pending: 0,
                running: 0,
                completed: 0,
                failed: 0,
                total: 0
            };
            stats.forEach(stat => {
                result[stat._id] = stat.count;
                result.total += stat.count;
            });
            return result;
        }
        catch (error) {
            console.error('Failed to get job statistics:', error);
            throw error;
        }
    }
}
exports.JobStatusService = JobStatusService;
exports.jobStatusService = JobStatusService.getInstance();
