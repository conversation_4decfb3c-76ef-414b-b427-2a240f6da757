/**
 * Shared logic for api-server and scraper-engine
 * @params service - The name of the service
 * @returns Health status of the service
 */
export function getHealthStatus(service: string) {
  return {
    status: "ok",
    service,
    timestamp: new Date().toISOString(),
  };
}

// Export queue functionality
export * from './queues';

// Export services
export * from './services/jobStatusService';
