import { Queue, Worker, QueueEvents } from 'bullmq';
import { config } from '@web-analyzer/config';
import { QueueNames, ScrapeJobData, ScrapeJobResult } from './types';

// Parse Redis URL if provided, otherwise use individual config values
function parseRedisConfig() {
  const redisUrl = config.redisUrl;

  if (redisUrl && redisUrl !== 'redis://localhost:6379') {
    // Parse Redis URL (e.g., redis://username:password@host:port/db)
    try {
      const url = new URL(redisUrl);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 6379,
        password: url.password || config.redisPassword || undefined,
        username: url.username || config.redisUsername || undefined,
        db: parseInt(url.pathname.slice(1)) || config.redisDB || 0,
        // BullMQ requires maxRetriesPerRequest to be null for optimal performance
        maxRetriesPerRequest: null,
        enableOfflineQueue: false, // Disable for better BullMQ compatibility
        connectTimeout: config.bullMQRedis.connectTimeout,
        retryDelayOnFailover: config.bullMQRedis.retryStrategy,
        maxLoadingTimeout: config.bullMQRedis.maxLoadingRetryTime,
      };
    } catch (error) {
      console.warn('Failed to parse Redis URL, using default config:', error);
    }
  }

  // Use individual config values
  return {
    host: config.bullMQRedis.host,
    port: config.bullMQRedis.port,
    password: config.bullMQRedis.password || config.redisPassword || undefined,
    username: config.bullMQRedis.username || config.redisUsername || undefined,
    db: config.bullMQRedis.db || config.redisDB || 0,
    // BullMQ requires maxRetriesPerRequest to be null for optimal performance
    maxRetriesPerRequest: null,
    enableOfflineQueue: false, // Disable for better BullMQ compatibility
    connectTimeout: config.bullMQRedis.connectTimeout,
    retryDelayOnFailover: config.bullMQRedis.retryStrategy,
    maxLoadingTimeout: config.bullMQRedis.maxLoadingRetryTime,
  };
}

// Redis connection configuration for BullMQ
export const redisConnection = parseRedisConfig();

// Create queues
export const scrapeJobsQueue = new Queue<ScrapeJobData>(QueueNames.SCRAPE_JOBS, {
  connection: redisConnection,
  defaultJobOptions: {
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50,      // Keep last 50 failed jobs
    attempts: 3,           // Retry failed jobs 3 times
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
});

// Queue events for monitoring
export const scrapeJobsQueueEvents = new QueueEvents(QueueNames.SCRAPE_JOBS, {
  connection: redisConnection,
});

// Helper function to add a scrape job
export const addScrapeJob = async (
  jobData: ScrapeJobData,
  options?: {
    delay?: number;
    priority?: number;
  }
) => {
  const job = await scrapeJobsQueue.add(
    'scrape-website',
    jobData,
    {
      delay: options?.delay,
      priority: options?.priority,
    }
  );

  console.log(`📋 Added scrape job ${job.id} for URL: ${jobData.url}`);
  return job;
};

// Helper function to get job status
export const getJobStatus = async (jobId: string) => {
  const job = await scrapeJobsQueue.getJob(jobId);
  if (!job) {
    return null;
  }

  return {
    id: job.id,
    name: job.name,
    data: job.data,
    progress: job.progress,
    returnvalue: job.returnvalue,
    failedReason: job.failedReason,
    processedOn: job.processedOn,
    finishedOn: job.finishedOn,
    opts: job.opts,
  };
};

// Helper function to get queue stats
export const getQueueStats = async () => {
  const waiting = await scrapeJobsQueue.getWaiting();
  const active = await scrapeJobsQueue.getActive();
  const completed = await scrapeJobsQueue.getCompleted();
  const failed = await scrapeJobsQueue.getFailed();

  return {
    waiting: waiting.length,
    active: active.length,
    completed: completed.length,
    failed: failed.length,
    total: waiting.length + active.length + completed.length + failed.length,
  };
};

// Graceful shutdown
export const closeQueues = async () => {
  console.log('🔄 Closing queue connections...');
  await scrapeJobsQueue.close();
  await scrapeJobsQueueEvents.close();
  console.log('✅ Queue connections closed');
};
