import { <PERSON>rapeJob } from '@web-analyzer/db-models';
import { scrapeJobsQueueEvents } from '../queues/connection';

export class JobStatusService {
  private static instance: JobStatusService;
  private statusListeners: Map<string, (status: any) => void> = new Map();

  private constructor() {
    this.setupQueueEventListeners();
  }

  public static getInstance(): JobStatusService {
    if (!JobStatusService.instance) {
      JobStatusService.instance = new JobStatusService();
    }
    return JobStatusService.instance;
  }

  private setupQueueEventListeners() {
    // Listen to queue events and update database accordingly
    scrapeJobsQueueEvents.on('waiting', async ({ jobId }) => {
      console.log(`📋 Job ${jobId} is waiting`);
      await this.updateJobStatusByQueueId(jobId, 'pending');
    });

    scrapeJobsQueueEvents.on('active', async ({ jobId }) => {
      console.log(`⚡ Job ${jobId} is active`);
      await this.updateJobStatusByQueueId(jobId, 'running');
    });

    scrapeJobsQueueEvents.on('completed', async ({ jobId, returnvalue }) => {
      console.log(`✅ Job ${jobId} completed`);
      await this.updateJobStatusByQueueId(jobId, 'completed', returnvalue);
    });

    scrapeJobsQueueEvents.on('failed', async ({ jobId, failedReason }) => {
      console.log(`❌ Job ${jobId} failed: ${failedReason}`);
      await this.updateJobStatusByQueueId(jobId, 'failed', null, failedReason);
    });

    scrapeJobsQueueEvents.on('progress', async ({ jobId, data }) => {
      console.log(`📊 Job ${jobId} progress: ${data}%`);
      // Optionally store progress in database or emit to real-time listeners
      this.notifyStatusListeners(jobId, { progress: data });
    });
  }

  private async updateJobStatusByQueueId(
    queueJobId: string,
    status: 'pending' | 'running' | 'completed' | 'failed',
    result?: any,
    error?: string
  ) {
    try {
      const updateData: any = { status };
      if (result) updateData.result = result;
      if (error) updateData.error = error;

      const job = await ScrapeJob.findOneAndUpdate(
        { queueJobId },
        updateData,
        { new: true }
      );

      if (job) {
        this.notifyStatusListeners(job._id?.toString() || '', {
          status,
          result,
          error,
          updatedAt: new Date().toISOString()
        });
      }
    } catch (dbError) {
      console.error('Failed to update job status in database:', dbError);
    }
  }

  public async updateJobStatus(
    jobId: string,
    status: 'pending' | 'running' | 'completed' | 'failed',
    result?: any,
    error?: string
  ) {
    try {
      const updateData: any = { status };
      if (result) updateData.result = result;
      if (error) updateData.error = error;

      const job = await ScrapeJob.findByIdAndUpdate(jobId, updateData, { new: true });

      if (job) {
        this.notifyStatusListeners(jobId, {
          status,
          result,
          error,
          updatedAt: new Date().toISOString()
        });
      }

      return job;
    } catch (dbError) {
      console.error('Failed to update job status:', dbError);
      throw dbError;
    }
  }

  public async getJobStatus(jobId: string) {
    try {
      const job = await ScrapeJob.findById(jobId).populate('userId', 'name email');
      return job;
    } catch (error) {
      console.error('Failed to get job status:', error);
      throw error;
    }
  }

  public async getJobsByUser(userId: string, options: {
    status?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { status, limit = 50, offset = 0 } = options;

      const filter: any = { userId };
      if (status) filter.status = status;

      const jobs = await ScrapeJob.find(filter)
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(offset);

      const total = await ScrapeJob.countDocuments(filter);

      return {
        jobs,
        total,
        hasMore: total > offset + limit
      };
    } catch (error) {
      console.error('Failed to get jobs by user:', error);
      throw error;
    }
  }

  public subscribeToJobStatus(jobId: string, callback: (status: any) => void) {
    this.statusListeners.set(jobId, callback);
  }

  public unsubscribeFromJobStatus(jobId: string) {
    this.statusListeners.delete(jobId);
  }

  private notifyStatusListeners(jobId: string, status: any) {
    const listener = this.statusListeners.get(jobId);
    if (listener) {
      listener(status);
    }
  }

  public async getJobStatistics() {
    try {
      const stats = await ScrapeJob.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {
        pending: 0,
        running: 0,
        completed: 0,
        failed: 0,
        total: 0
      };

      stats.forEach(stat => {
        result[stat._id as keyof typeof result] = stat.count;
        result.total += stat.count;
      });

      return result;
    } catch (error) {
      console.error('Failed to get job statistics:', error);
      throw error;
    }
  }
}

export const jobStatusService = JobStatusService.getInstance();
