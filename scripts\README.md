# Scripts Directory

This directory contains all the utility scripts for the Web Analyzer Platform. The scripts have been organized here to keep the root directory clean and maintainable.

## 📁 Script Files

### 🚀 Startup Scripts
- **`start.js`** - Main startup script for the platform
- **`start.bat`** - Windows batch wrapper for easy startup
- **`dev-start.js`** - Development environment startup with Redis management

### 🔧 Setup & Configuration
- **`setup-project.js`** - Interactive project setup and configuration
- **`start-redis.js`** - Redis container management and startup

### 🧪 Testing Scripts
- **`test-docker.js`** - Cross-platform Docker integration tests
- **`test-docker.sh`** - Linux/Mac shell script for Docker testing
- **`test-docker.bat`** - Windows batch script for Docker testing
- **`test-redis-connection.js`** - Redis connection verification

## 🎯 Usage

### From Root Directory
All scripts can be run from the root directory using npm commands:

```bash
# Main commands
npm start                    # Start the platform
npm run setup               # Interactive setup
npm run dev                 # Development environment

# Testing commands
npm run docker:test         # Run integration tests
npm run redis:test          # Test Redis connection
npm run redis:start         # Start Redis container
```

### Direct Script Execution
You can also run scripts directly from the scripts directory:

```bash
# From root directory
node scripts/start.js
node scripts/setup-project.js
node scripts/test-docker.js

# From scripts directory
cd scripts
node start.js
node setup-project.js
node test-docker.js
```

### Windows Users
For Windows users, there's a convenient batch file in the root directory:

```batch
# Double-click or run from command prompt
start.bat
```

This is a simple wrapper that calls `scripts/start.js`.

## 🔧 Script Dependencies

### Node.js Scripts
All scripts require Node.js and use the following dependencies:
- **Built-in modules**: `child_process`, `fs`, `path`, `readline`
- **External dependencies**: Available through the workspace packages

### Docker Scripts
Docker-related scripts require:
- Docker Engine
- Docker Compose
- Access to Docker daemon

### Redis Scripts
Redis scripts require:
- Docker (for containerized Redis)
- Network access to Redis instance

## 🛠️ Development

### Adding New Scripts
When adding new scripts to this directory:

1. **Use consistent naming**: `action-target.js` (e.g., `test-redis.js`)
2. **Add proper headers**: Include description and usage comments
3. **Handle errors gracefully**: Provide clear error messages
4. **Update package.json**: Add npm script shortcuts if needed
5. **Update this README**: Document the new script

### Script Template
```javascript
#!/usr/bin/env node

/**
 * Script Name - Brief Description
 * Detailed description of what the script does
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Main function
async function main() {
  log.title('Script Title');
  log.title('============');
  console.log();
  
  try {
    // Script logic here
    log.success('Script completed successfully!');
  } catch (error) {
    log.error(`Script failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\nScript interrupted by user.');
  process.exit(1);
});

// Run main function
main().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
```

## 📚 Related Documentation

- **[Development Guide](../DEVELOPMENT_GUIDE.md)** - Development setup and troubleshooting
- **[Docker Setup](../DOCKER_SETUP.md)** - Docker configuration and usage
- **[Commands Reference](../COMMANDS.md)** - All available commands
- **[Main README](../README.md)** - Project overview and quick start

## 🔄 File Organization

This organization keeps the root directory clean while maintaining easy access to all functionality:

```
Root Directory:
├── start.bat              # Windows launcher (calls scripts/start.js)
├── package.json           # npm scripts reference scripts/ directory
└── scripts/               # All utility scripts organized here
    ├── README.md          # This file
    ├── start.js           # Main startup script
    ├── setup-project.js   # Interactive setup
    ├── dev-start.js       # Development startup
    ├── test-*.js          # Testing scripts
    └── *-redis.js         # Redis management scripts
```

This structure provides:
- ✅ **Clean root directory**
- ✅ **Organized script management**
- ✅ **Easy script discovery**
- ✅ **Consistent naming conventions**
- ✅ **Backward compatibility** (npm scripts still work the same way)
