#!/usr/bin/env node

/**
 * Web Analyzer Platform - Project Setup Script
 * Automated setup for the entire project with Docker
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`),
  subtitle: (msg) => console.log(`${colors.magenta}${msg}${colors.reset}`)
};

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to ask questions
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Helper function to check if command exists
function commandExists(command) {
  try {
    execSync(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, {
      stdio: options.silent ? 'ignore' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
      log.error(error.message);
    }
    throw error;
  }
}

// Main setup function
async function setupProject() {
  log.title('🚀 Web Analyzer Platform - Project Setup');
  log.title('==========================================');
  console.log();

  log.info('This script will help you set up the Web Analyzer Platform with Docker.');
  log.info('The setup includes API server, scraper engine, Redis, and all dependencies.');
  console.log();

  try {
    // Step 1: Check prerequisites
    log.subtitle('Step 1: Checking Prerequisites');
    log.info('Checking required software...');

    const prerequisites = [
      { name: 'Node.js', command: 'node', version: '--version' },
      { name: 'Docker', command: 'docker', version: '--version' },
      { name: 'Docker Compose', command: 'docker-compose', version: '--version' }
    ];

    for (const prereq of prerequisites) {
      if (commandExists(prereq.command)) {
        try {
          const version = execSync(`${prereq.command} ${prereq.version}`, { encoding: 'utf8' }).trim();
          log.success(`${prereq.name}: ${version}`);
        } catch {
          log.success(`${prereq.name}: Installed`);
        }
      } else {
        log.error(`${prereq.name} is not installed or not in PATH`);
        log.error('Please install the required software and try again.');
        process.exit(1);
      }
    }
    console.log();

    // Step 2: Environment Configuration
    log.subtitle('Step 2: Environment Configuration');

    if (!fs.existsSync('.env.docker')) {
      log.info('Setting up environment configuration...');

      if (fs.existsSync('.env')) {
        fs.copyFileSync('.env', '.env.docker');
        log.success('Copied existing .env to .env.docker');
      } else {
        log.warning('No .env file found. You may need to configure environment variables manually.');
      }
    } else {
      log.success('Environment configuration already exists');
    }

    // Ask for MongoDB URI if not set
    if (fs.existsSync('.env.docker')) {
      const envContent = fs.readFileSync('.env.docker', 'utf8');
      if (!envContent.includes('mongodb+srv://') || envContent.includes('your-connection-string')) {
        console.log();
        log.warning('MongoDB Atlas connection string needs to be configured.');
        const mongoUri = await askQuestion('Enter your MongoDB Atlas URI (or press Enter to skip): ');

        if (mongoUri) {
          const updatedEnv = envContent.replace(
            /MONGO_URI=.*/,
            `MONGO_URI=${mongoUri}`
          );
          fs.writeFileSync('.env.docker', updatedEnv);
          log.success('MongoDB URI updated in .env.docker');
        }
      }
    }
    console.log();

    // Step 3: Docker Setup
    log.subtitle('Step 3: Docker Setup');
    log.info('Checking Docker daemon...');

    try {
      execSafe('docker info', { silent: true });
      log.success('Docker daemon is running');
    } catch {
      log.error('Docker daemon is not running. Please start Docker and try again.');
      process.exit(1);
    }

    // Clean up any existing containers
    log.info('Cleaning up any existing containers...');
    try {
      execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml down -v --remove-orphans', { silent: true });
    } catch {
      // Ignore cleanup errors
    }
    log.success('Cleanup completed');
    console.log();

    // Step 4: Build and Start Services
    log.subtitle('Step 4: Building and Starting Services');
    log.info('This may take several minutes for the first build...');
    log.info('Building Docker images and starting services...');

    try {
      execSafe('docker-compose -f infrastructure/docker/docker-compose.dev.yml --env-file .env.docker up -d --build');
      log.success('Services built and started successfully');
    } catch (error) {
      log.error('Failed to build and start services');
      throw error;
    }
    console.log();

    // Step 5: Wait for Services
    log.subtitle('Step 5: Waiting for Services to Initialize');
    log.info('Waiting for services to be ready...');

    const spinnerChars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let spinnerIndex = 0;

    const spinner = setInterval(() => {
      process.stdout.write(`\r${colors.cyan}${spinnerChars[spinnerIndex]}${colors.reset} Initializing services...`);
      spinnerIndex = (spinnerIndex + 1) % spinnerChars.length;
    }, 100);

    await new Promise(resolve => setTimeout(resolve, 45000)); // Wait 45 seconds

    clearInterval(spinner);
    process.stdout.write('\r' + ' '.repeat(50) + '\r'); // Clear spinner line
    log.success('Services initialization completed');
    console.log();

    // Step 6: Verify Installation
    log.subtitle('Step 6: Verifying Installation');
    log.info('Running integration tests...');

    try {
      execSafe('node scripts/test-docker.js');
    } catch (error) {
      log.warning('Some tests failed, but the basic setup is complete.');
      log.info('You can check the logs with: npm run docker:logs');
    }
    console.log();

    // Step 7: Success and Next Steps
    log.subtitle('🎉 Setup Complete!');
    log.success('Web Analyzer Platform is now running with Docker!');
    console.log();

    console.log('📋 Available Services:');
    console.log('  • API Server:        http://localhost:5000');
    console.log('  • Scraper Engine:     http://localhost:5001');
    console.log('  • Redis Commander:    http://localhost:8081');
    console.log();

    console.log('🛠️  Useful Commands:');
    console.log('  • Start services:     npm start');
    console.log('  • Stop services:      npm stop');
    console.log('  • View logs:          npm run docker:logs');
    console.log('  • Run tests:          npm run docker:test');
    console.log('  • Clean up:           npm run docker:clean');
    console.log();

    console.log('📚 Documentation:');
    console.log('  • Docker Setup:       DOCKER_SETUP.md');
    console.log('  • Job Queue:          JOB_QUEUE_INTEGRATION.md');
    console.log();

    console.log('🔧 Next Steps:');
    console.log('  1. Test the API endpoints using the provided URLs');
    console.log('  2. Check Redis Commander for queue monitoring');
    console.log('  3. Review the documentation files for detailed usage');
    console.log('  4. Configure your MongoDB Atlas connection if not done');
    console.log();

    const openBrowser = await askQuestion('Would you like to open the API server in your browser? (y/N): ');
    if (openBrowser.toLowerCase() === 'y' || openBrowser.toLowerCase() === 'yes') {
      const { spawn } = require('child_process');
      const url = 'http://localhost:5000';

      let command;
      switch (process.platform) {
        case 'darwin': command = 'open'; break;
        case 'win32': command = 'start'; break;
        default: command = 'xdg-open'; break;
      }

      try {
        spawn(command, [url], { detached: true, stdio: 'ignore' });
        log.success(`Opening ${url} in your default browser...`);
      } catch {
        log.info(`Please open ${url} in your browser manually.`);
      }
    }

  } catch (error) {
    log.error('Setup failed!');
    log.error(error.message);
    console.log();
    log.info('Troubleshooting tips:');
    log.info('1. Make sure Docker is running');
    log.info('2. Check if ports 5000, 5001, 6379 are available');
    log.info('3. Verify your MongoDB connection string');
    log.info('4. Run "npm run docker:clean" to clean up and try again');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\nSetup interrupted by user.');
  rl.close();
  process.exit(1);
});

// Run setup
setupProject().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  rl.close();
  process.exit(1);
});
