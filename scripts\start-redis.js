#!/usr/bin/env node

/**
 * Start Redis for Development
 * Starts Redis using Docker if not already running
 */

const { execSync, spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Helper function to check if command exists
function commandExists(command) {
  try {
    execSync(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, { 
      stdio: options.silent ? 'ignore' : 'inherit',
      encoding: 'utf8',
      ...options 
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Check if Redis is running
function isRedisRunning() {
  try {
    // Check if Redis container is running
    const output = execSync('docker ps --filter "name=redis" --format "{{.Names}}"', { 
      encoding: 'utf8', 
      stdio: 'pipe' 
    });
    return output.includes('redis');
  } catch {
    return false;
  }
}

// Check if Redis is accessible
function isRedisAccessible() {
  try {
    execSync('docker exec web-analyzer-redis-dev redis-cli ping', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Start Redis container
function startRedis() {
  try {
    log.info('Starting Redis container...');
    execSafe('docker run -d --name web-analyzer-redis-dev -p 6379:6379 redis:7-alpine redis-server --appendonly yes');
    log.success('Redis container started successfully');
    return true;
  } catch (error) {
    log.error('Failed to start Redis container');
    return false;
  }
}

// Main function
async function main() {
  log.title('🔴 Redis Startup Script');
  log.title('======================');
  console.log();

  // Check if Docker is available
  if (!commandExists('docker')) {
    log.error('Docker is not installed or not in PATH');
    log.info('Please install Docker to use Redis');
    process.exit(1);
  }

  // Check if Docker daemon is running
  try {
    execSafe('docker info', { silent: true });
  } catch {
    log.error('Docker daemon is not running');
    log.info('Please start Docker and try again');
    process.exit(1);
  }

  // Check if Redis is already running
  if (isRedisRunning()) {
    log.info('Redis container is already running');
    
    // Check if Redis is accessible
    if (isRedisAccessible()) {
      log.success('Redis is running and accessible on port 6379');
      console.log();
      log.info('You can now start your development servers:');
      log.info('  • API Server: pnpm --filter api-server dev');
      log.info('  • Scraper Engine: pnpm --filter scraper-engine dev');
      process.exit(0);
    } else {
      log.warning('Redis container exists but is not accessible');
      log.info('Attempting to restart Redis...');
      
      try {
        execSafe('docker restart web-analyzer-redis-dev', { silent: true });
        log.success('Redis container restarted');
      } catch {
        log.error('Failed to restart Redis container');
        process.exit(1);
      }
    }
  } else {
    log.info('Redis is not running');
    
    // Try to start existing container first
    try {
      execSafe('docker start web-analyzer-redis-dev', { silent: true });
      log.success('Started existing Redis container');
    } catch {
      // Container doesn't exist, create new one
      if (!startRedis()) {
        process.exit(1);
      }
    }
  }

  // Wait for Redis to be ready
  log.info('Waiting for Redis to be ready...');
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    try {
      execSync('docker exec web-analyzer-redis-dev redis-cli ping', { stdio: 'ignore' });
      log.success('Redis is ready!');
      break;
    } catch {
      attempts++;
      if (attempts < maxAttempts) {
        log.info(`Waiting for Redis... (${attempts}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      } else {
        log.error('Redis failed to start properly');
        process.exit(1);
      }
    }
  }

  console.log();
  log.success('🎉 Redis is running successfully!');
  console.log();
  log.info('Redis connection details:');
  log.info('  • Host: localhost');
  log.info('  • Port: 6379');
  log.info('  • Container: web-analyzer-redis-dev');
  console.log();
  log.info('Next steps:');
  log.info('  1. Start API Server: pnpm --filter api-server dev');
  log.info('  2. Start Scraper Engine: pnpm --filter scraper-engine dev');
  log.info('  3. Or use Docker: npm start');
  console.log();
  log.info('To stop Redis: docker stop web-analyzer-redis-dev');
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\nRedis startup interrupted by user.');
  process.exit(1);
});

// Run main function
main().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
