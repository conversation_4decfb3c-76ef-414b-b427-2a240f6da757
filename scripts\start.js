#!/usr/bin/env node

/**
 * Quick Start Script for Web Analyzer Platform
 * One-command project startup with <PERSON>er
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Helper function to check if command exists
function commandExists(command) {
  try {
    execSync(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Helper function to execute command safely
function execSafe(command, options = {}) {
  try {
    return execSync(command, { 
      stdio: options.silent ? 'ignore' : 'inherit',
      encoding: 'utf8',
      ...options 
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
}

// Check if services are already running
function checkServicesRunning() {
  try {
    execSafe('docker-compose -f docker-compose.dev.yml ps --services --filter "status=running"', { silent: true });
    const output = execSync('docker-compose -f docker-compose.dev.yml ps --services --filter "status=running"', { 
      encoding: 'utf8', 
      stdio: 'pipe' 
    });
    return output.trim().length > 0;
  } catch {
    return false;
  }
}

// Main startup function
async function startProject() {
  log.title('🚀 Web Analyzer Platform - Quick Start');
  log.title('======================================');
  console.log();

  try {
    // Quick prerequisite check
    if (!commandExists('docker') || !commandExists('docker-compose')) {
      log.error('Docker or Docker Compose is not installed!');
      log.info('Please run: npm run setup');
      process.exit(1);
    }

    // Check if Docker daemon is running
    try {
      execSafe('docker info', { silent: true });
    } catch {
      log.error('Docker daemon is not running!');
      log.info('Please start Docker and try again.');
      process.exit(1);
    }

    // Check if services are already running
    if (checkServicesRunning()) {
      log.warning('Services are already running!');
      log.info('Current services:');
      execSafe('docker-compose -f docker-compose.dev.yml ps');
      console.log();
      log.info('To restart services: npm restart');
      log.info('To stop services: npm stop');
      return;
    }

    // Check environment file
    if (!fs.existsSync('.env.docker')) {
      log.warning('Environment file not found!');
      log.info('Running initial setup...');
      execSafe('node scripts/setup-project.js');
      return;
    }

    // Start services
    log.info('Starting Web Analyzer Platform...');
    log.info('This may take a moment...');
    
    const spinnerChars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let spinnerIndex = 0;
    
    const spinner = setInterval(() => {
      process.stdout.write(`\r${colors.cyan}${spinnerChars[spinnerIndex]}${colors.reset} Starting services...`);
      spinnerIndex = (spinnerIndex + 1) % spinnerChars.length;
    }, 100);

    try {
      execSafe('docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d', { silent: true });
      
      clearInterval(spinner);
      process.stdout.write('\r' + ' '.repeat(50) + '\r'); // Clear spinner line
      log.success('Services started successfully!');
    } catch (error) {
      clearInterval(spinner);
      process.stdout.write('\r' + ' '.repeat(50) + '\r'); // Clear spinner line
      log.error('Failed to start services');
      throw error;
    }

    // Wait a moment for services to initialize
    log.info('Waiting for services to initialize...');
    await new Promise(resolve => setTimeout(resolve, 15000));

    // Quick health check
    log.info('Performing quick health check...');
    
    const healthChecks = [
      { name: 'Redis', command: 'docker exec web-analyzer-redis-dev redis-cli ping' },
      { name: 'API Server', command: 'curl -s -f http://localhost:5000/health' },
      { name: 'Scraper Engine', command: 'curl -s -f http://localhost:5001/health' }
    ];

    let healthyServices = 0;
    for (const check of healthChecks) {
      try {
        execSafe(check.command, { silent: true });
        log.success(`${check.name}: ✅ Healthy`);
        healthyServices++;
      } catch {
        log.warning(`${check.name}: ⚠️  Starting up...`);
      }
    }

    console.log();

    // Show service information
    log.title('🎉 Platform Started Successfully!');
    console.log();
    
    console.log('📋 Available Services:');
    console.log('  • API Server:        http://localhost:5000');
    console.log('  • Scraper Engine:     http://localhost:5001');
    console.log('  • Redis Commander:    http://localhost:8081');
    console.log();
    
    console.log('🛠️  Quick Commands:');
    console.log('  • View logs:          npm run docker:logs');
    console.log('  • Stop services:      npm stop');
    console.log('  • Run tests:          npm run docker:test');
    console.log('  • Full setup:         npm run setup');
    console.log();

    if (healthyServices < healthChecks.length) {
      log.warning('Some services are still starting up. This is normal for the first run.');
      log.info('Services may take 1-2 minutes to be fully ready.');
      log.info('Run "npm run docker:test" to verify when all services are ready.');
    }

    // Show container status
    log.info('Container Status:');
    execSafe('docker-compose -f docker-compose.dev.yml ps');

  } catch (error) {
    log.error('Failed to start the platform!');
    log.error(error.message);
    console.log();
    log.info('Troubleshooting:');
    log.info('1. Run "npm run setup" for full setup');
    log.info('2. Check Docker is running');
    log.info('3. Verify ports 5000, 5001, 6379 are available');
    log.info('4. Run "npm run docker:clean" to reset');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\nStartup interrupted by user.');
  process.exit(1);
});

// Run startup
startProject().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
