@echo off
setlocal enabledelayedexpansion

REM Docker Integration Test Script for Windows
REM Tests the complete Docker setup with all services

echo 🐳 Docker Integration Test Script
echo =================================
echo.

REM Check prerequisites
echo [INFO] Checking prerequisites...

where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed or not in PATH
    exit /b 1
)

where docker-compose >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not installed or not in PATH
    exit /b 1
)

echo [SUCCESS] Prerequisites check passed
echo.

REM Check Docker daemon
echo [INFO] Checking Docker daemon...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker daemon is not running
    exit /b 1
)
echo [SUCCESS] Docker daemon is running
echo.

REM Check environment file
echo [INFO] Checking environment configuration...
if not exist ".env.docker" (
    echo [WARNING] .env.docker not found, creating from template...
    if exist ".env" (
        copy .env .env.docker >nul
    ) else (
        echo [ERROR] No environment file found. Please create .env.docker
        exit /b 1
    )
)
echo [SUCCESS] Environment configuration found
echo.

REM Clean up any existing containers
echo [INFO] Cleaning up existing containers...
docker-compose -f docker-compose.dev.yml down -v --remove-orphans >nul 2>&1
echo [SUCCESS] Cleanup completed
echo.

REM Build and start services
echo [INFO] Building and starting Docker services...
docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d --build
if %errorlevel% neq 0 (
    echo [ERROR] Failed to start services
    exit /b 1
)
echo [SUCCESS] Services started successfully
echo.

REM Wait for services to be ready
echo [INFO] Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Test Redis
echo [INFO] Testing Redis connection...
docker exec web-analyzer-redis-dev redis-cli ping >nul 2>&1
if %errorlevel% eq 0 (
    echo [SUCCESS] Redis is responding
    set REDIS_STATUS=0
) else (
    echo [ERROR] Redis is not responding
    set REDIS_STATUS=1
)
echo.

REM Test API Server
echo [INFO] Testing API Server...
set API_STATUS=1
for /l %%i in (1,1,10) do (
    curl -s -f "http://localhost:5000/health" >nul 2>&1
    if !errorlevel! eq 0 (
        echo [SUCCESS] API Server is healthy
        set API_STATUS=0
        goto :api_done
    )
    echo [WARNING] API Server not ready, attempt %%i/10
    timeout /t 5 /nobreak >nul
)
echo [ERROR] API Server failed health check
:api_done
echo.

REM Test Scraper Engine
echo [INFO] Testing Scraper Engine...
set SCRAPER_STATUS=1
for /l %%i in (1,1,10) do (
    curl -s -f "http://localhost:5001/health" >nul 2>&1
    if !errorlevel! eq 0 (
        echo [SUCCESS] Scraper Engine is healthy
        set SCRAPER_STATUS=0
        goto :scraper_done
    )
    echo [WARNING] Scraper Engine not ready, attempt %%i/10
    timeout /t 5 /nobreak >nul
)
echo [ERROR] Scraper Engine failed health check
:scraper_done
echo.

REM Test Redis Commander (optional)
curl -s -f "http://localhost:8081" >nul 2>&1
if %errorlevel% eq 0 (
    echo [SUCCESS] Redis Commander is accessible
) else (
    echo [WARNING] Redis Commander is not accessible (this is optional)
)
echo.

REM Test job queue integration
echo [INFO] Testing job queue integration...
set JOB_QUEUE_STATUS=1

REM Create a test user first
curl -s -X POST http://localhost:5000/api/users -H "Content-Type: application/json" -d "{\"name\": \"Test User\", \"email\": \"<EMAIL>\"}" > temp_user.json 2>nul
if exist temp_user.json (
    findstr "success.*true" temp_user.json >nul
    if !errorlevel! eq 0 (
        echo [SUCCESS] Test user created
        
        REM Create a test scrape job
        curl -s -X POST http://localhost:5000/api/queue/scrape -H "Content-Type: application/json" -d "{\"url\": \"https://example.com\", \"userId\": \"test-user-id\", \"options\": {\"screenshot\": false}}" > temp_job.json 2>nul
        if exist temp_job.json (
            findstr "success.*true" temp_job.json >nul
            if !errorlevel! eq 0 (
                echo [SUCCESS] Test scrape job created successfully
                set JOB_QUEUE_STATUS=0
            ) else (
                echo [ERROR] Failed to create test scrape job
            )
            del temp_job.json >nul 2>&1
        )
    ) else (
        echo [ERROR] Failed to create test user
    )
    del temp_user.json >nul 2>&1
)
echo.

REM Test queue statistics
echo [INFO] Testing queue statistics...
curl -s http://localhost:5000/api/queue/stats > temp_stats.json 2>nul
if exist temp_stats.json (
    findstr "success.*true" temp_stats.json >nul
    if !errorlevel! eq 0 (
        echo [SUCCESS] Queue statistics endpoint working
    ) else (
        echo [WARNING] Queue statistics endpoint not responding properly
    )
    del temp_stats.json >nul 2>&1
)
echo.

REM Show container status
echo [INFO] Container status:
docker-compose -f docker-compose.dev.yml ps
echo.

REM Summary
echo 🏁 Test Summary
echo ===============

if %API_STATUS% eq 0 (
    echo [SUCCESS] ✅ API Server: PASSED
) else (
    echo [ERROR] ❌ API Server: FAILED
)

if %SCRAPER_STATUS% eq 0 (
    echo [SUCCESS] ✅ Scraper Engine: PASSED
) else (
    echo [ERROR] ❌ Scraper Engine: FAILED
)

if %REDIS_STATUS% eq 0 (
    echo [SUCCESS] ✅ Redis: PASSED
) else (
    echo [ERROR] ❌ Redis: FAILED
)

if %JOB_QUEUE_STATUS% eq 0 (
    echo [SUCCESS] ✅ Job Queue Integration: PASSED
) else (
    echo [ERROR] ❌ Job Queue Integration: FAILED
)

echo.

REM Overall result
set /a TOTAL_FAILED=%API_STATUS%+%SCRAPER_STATUS%+%REDIS_STATUS%+%JOB_QUEUE_STATUS%
if %TOTAL_FAILED% eq 0 (
    echo [SUCCESS] 🎉 All tests PASSED! Docker integration is working correctly.
    echo.
    echo Services are running at:
    echo   • API Server: http://localhost:5000
    echo   • Scraper Engine: http://localhost:5001
    echo   • Redis Commander: http://localhost:8081
    echo.
    echo To stop services: docker-compose -f docker-compose.dev.yml down
    exit /b 0
) else (
    echo [ERROR] ❌ Some tests FAILED. Please check the logs above.
    echo.
    echo To view logs: docker-compose -f docker-compose.dev.yml logs
    echo To stop services: docker-compose -f docker-compose.dev.yml down
    exit /b 1
)
