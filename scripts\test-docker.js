#!/usr/bin/env node

/**
 * Docker Integration Test Script (Node.js)
 * Cross-platform test script for Docker setup
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Helper function to check if command exists
function commandExists(command) {
  try {
    execSync(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Helper function to execute command with timeout
function execWithTimeout(command, timeout = 30000) {
  try {
    return execSync(command, {
      timeout,
      stdio: 'pipe',
      encoding: 'utf8'
    });
  } catch (error) {
    throw error;
  }
}

// Helper function to test HTTP endpoint
async function testEndpoint(url, maxAttempts = 10, delay = 5000) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        return true;
      }
    } catch (error) {
      // Endpoint not ready yet
    }

    if (attempt < maxAttempts) {
      log.warning(`Endpoint ${url} not ready, attempt ${attempt}/${maxAttempts}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  return false;
}

// Helper function to make HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      body: options.body ? JSON.stringify(options.body) : undefined,
      ...options
    });

    if (response.ok) {
      return await response.json();
    }
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  } catch (error) {
    throw error;
  }
}

// Main test function
async function runTests() {
  log.title('🐳 Docker Integration Test Script');
  log.title('=================================');
  console.log();

  let testResults = {
    prerequisites: false,
    docker: false,
    redis: false,
    apiServer: false,
    scraperEngine: false,
    jobQueue: false
  };

  try {
    // Check prerequisites
    log.info('Checking prerequisites...');

    if (!commandExists('docker')) {
      log.error('Docker is not installed or not in PATH');
      process.exit(1);
    }

    if (!commandExists('docker-compose')) {
      log.error('Docker Compose is not installed or not in PATH');
      process.exit(1);
    }

    testResults.prerequisites = true;
    log.success('Prerequisites check passed');
    console.log();

    // Check Docker daemon
    log.info('Checking Docker daemon...');
    try {
      execWithTimeout('docker info', 10000);
      testResults.docker = true;
      log.success('Docker daemon is running');
    } catch {
      log.error('Docker daemon is not running');
      process.exit(1);
    }
    console.log();

    // Check environment file
    log.info('Checking environment configuration...');
    if (!fs.existsSync('.env.docker')) {
      log.warning('.env.docker not found, creating from template...');
      if (fs.existsSync('.env')) {
        fs.copyFileSync('.env', '.env.docker');
      } else {
        log.error('No environment file found. Please create .env.docker');
        process.exit(1);
      }
    }
    log.success('Environment configuration found');
    console.log();

    // Clean up existing containers
    log.info('Cleaning up existing containers...');
    try {
      execSync('docker-compose -f infrastructure/docker/docker-compose.dev.yml down -v --remove-orphans', { stdio: 'ignore' });
    } catch {
      // Ignore cleanup errors
    }
    log.success('Cleanup completed');
    console.log();

    // Build and start services
    log.info('Building and starting Docker services...');
    log.info('This may take a few minutes for the first build...');
    try {
      execSync('docker-compose -f infrastructure/docker/docker-compose.dev.yml --env-file .env.docker up -d --build', {
        stdio: 'inherit'
      });
      log.success('Services started successfully');
    } catch (error) {
      log.error('Failed to start services');
      console.error(error.message);
      process.exit(1);
    }
    console.log();

    // Wait for services to be ready
    log.info('Waiting for services to be ready...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    // Test Redis
    log.info('Testing Redis connection...');
    try {
      execWithTimeout('docker exec web-analyzer-redis-dev redis-cli ping', 10000);
      testResults.redis = true;
      log.success('Redis is responding');
    } catch {
      log.error('Redis is not responding');
    }
    console.log();

    // Test API Server
    log.info('Testing API Server...');
    const apiHealthy = await testEndpoint('http://localhost:5000/health');
    if (apiHealthy) {
      testResults.apiServer = true;
      log.success('API Server is healthy');
    } else {
      log.error('API Server failed health check');
    }
    console.log();

    // Test Scraper Engine
    log.info('Testing Scraper Engine...');
    const scraperHealthy = await testEndpoint('http://localhost:5001/health');
    if (scraperHealthy) {
      testResults.scraperEngine = true;
      log.success('Scraper Engine is healthy');
    } else {
      log.error('Scraper Engine failed health check');
    }
    console.log();

    // Test Redis Commander (optional)
    try {
      const redisCommanderHealthy = await testEndpoint('http://localhost:8081', 3, 2000);
      if (redisCommanderHealthy) {
        log.success('Redis Commander is accessible');
      } else {
        log.warning('Redis Commander is not accessible (this is optional)');
      }
    } catch {
      log.warning('Redis Commander is not accessible (this is optional)');
    }
    console.log();

    // Test job queue integration
    log.info('Testing job queue integration...');
    try {
      // Create a test user
      const userResponse = await makeRequest('http://localhost:5000/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: { name: 'Test User', email: '<EMAIL>' }
      });

      if (userResponse.success) {
        const userId = userResponse.data._id;
        log.success(`Test user created: ${userId}`);

        // Create a test scrape job
        const jobResponse = await makeRequest('http://localhost:5000/api/queue/scrape', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: {
            url: 'https://example.com',
            userId: userId,
            options: { screenshot: false }
          }
        });

        if (jobResponse.success) {
          testResults.jobQueue = true;
          log.success('Test scrape job created successfully');
        } else {
          log.error('Failed to create test scrape job');
        }
      } else {
        log.error('Failed to create test user');
      }
    } catch (error) {
      log.error(`Job queue test failed: ${error.message}`);
    }
    console.log();

    // Test queue statistics
    log.info('Testing queue statistics...');
    try {
      const statsResponse = await makeRequest('http://localhost:5000/api/queue/stats');
      if (statsResponse.success) {
        log.success('Queue statistics endpoint working');
      } else {
        log.warning('Queue statistics endpoint not responding properly');
      }
    } catch {
      log.warning('Queue statistics endpoint not responding properly');
    }
    console.log();

    // Show container status
    log.info('Container status:');
    try {
      execSync('docker-compose -f infrastructure/docker/docker-compose.dev.yml ps', { stdio: 'inherit' });
    } catch {
      log.warning('Could not retrieve container status');
    }
    console.log();

  } catch (error) {
    log.error(`Test execution failed: ${error.message}`);
    process.exit(1);
  }

  // Summary
  log.title('🏁 Test Summary');
  log.title('===============');

  const results = [
    { name: 'Prerequisites', status: testResults.prerequisites },
    { name: 'Docker Daemon', status: testResults.docker },
    { name: 'Redis', status: testResults.redis },
    { name: 'API Server', status: testResults.apiServer },
    { name: 'Scraper Engine', status: testResults.scraperEngine },
    { name: 'Job Queue Integration', status: testResults.jobQueue }
  ];

  results.forEach(result => {
    if (result.status) {
      log.success(`✅ ${result.name}: PASSED`);
    } else {
      log.error(`❌ ${result.name}: FAILED`);
    }
  });

  console.log();

  // Overall result
  const allPassed = results.every(result => result.status);
  if (allPassed) {
    log.success('🎉 All tests PASSED! Docker integration is working correctly.');
    console.log();
    console.log('Services are running at:');
    console.log('  • API Server: http://localhost:5000');
    console.log('  • Scraper Engine: http://localhost:5001');
    console.log('  • Redis Commander: http://localhost:8081');
    console.log();
    console.log('To stop services: npm run docker:down');
    process.exit(0);
  } else {
    log.error('❌ Some tests FAILED. Please check the logs above.');
    console.log();
    console.log('To view logs: npm run docker:logs');
    console.log('To stop services: npm run docker:down');
    process.exit(1);
  }
}

// Add fetch polyfill for older Node.js versions
if (!global.fetch) {
  try {
    global.fetch = require('node-fetch');
  } catch {
    log.error('This script requires Node.js 18+ or the node-fetch package');
    process.exit(1);
  }
}

// Run tests
runTests().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
