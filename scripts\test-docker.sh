#!/bin/bash

# Docker Integration Test Script
# Tests the complete Docker setup with all services

set -e

echo "🐳 Docker Integration Test Script"
echo "================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists docker; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! command_exists docker-compose; then
    print_error "Docker Compose is not installed or not in PATH"
    exit 1
fi

print_success "Prerequisites check passed"
echo ""

# Check Docker daemon
print_status "Checking Docker daemon..."
if ! docker info >/dev/null 2>&1; then
    print_error "Docker daemon is not running"
    exit 1
fi
print_success "Docker daemon is running"
echo ""

# Check environment file
print_status "Checking environment configuration..."
if [ ! -f ".env.docker" ]; then
    print_warning ".env.docker not found, creating from template..."
    cp .env .env.docker 2>/dev/null || {
        print_error "No environment file found. Please create .env.docker"
        exit 1
    }
fi
print_success "Environment configuration found"
echo ""

# Clean up any existing containers
print_status "Cleaning up existing containers..."
docker-compose -f docker-compose.dev.yml down -v --remove-orphans >/dev/null 2>&1 || true
print_success "Cleanup completed"
echo ""

# Build and start services
print_status "Building and starting Docker services..."
if docker-compose -f docker-compose.dev.yml --env-file .env.docker up -d --build; then
    print_success "Services started successfully"
else
    print_error "Failed to start services"
    exit 1
fi
echo ""

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Function to test service health
test_service() {
    local service_name=$1
    local url=$2
    local max_attempts=10
    local attempt=1

    print_status "Testing $service_name..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            print_success "$service_name is healthy"
            return 0
        fi
        
        print_warning "$service_name not ready, attempt $attempt/$max_attempts"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed health check"
    return 1
}

# Test Redis
print_status "Testing Redis connection..."
if docker exec web-analyzer-redis-dev redis-cli ping >/dev/null 2>&1; then
    print_success "Redis is responding"
else
    print_error "Redis is not responding"
    REDIS_FAILED=1
fi
echo ""

# Test API Server
test_service "API Server" "http://localhost:5000/health"
API_STATUS=$?
echo ""

# Test Scraper Engine
test_service "Scraper Engine" "http://localhost:5001/health"
SCRAPER_STATUS=$?
echo ""

# Test Redis Commander (if available)
if curl -s -f "http://localhost:8081" >/dev/null 2>&1; then
    print_success "Redis Commander is accessible"
else
    print_warning "Redis Commander is not accessible (this is optional)"
fi
echo ""

# Test job queue integration
print_status "Testing job queue integration..."

# Create a test user first
USER_RESPONSE=$(curl -s -X POST http://localhost:5000/api/users \
    -H "Content-Type: application/json" \
    -d '{"name": "Test User", "email": "<EMAIL>"}' 2>/dev/null)

if echo "$USER_RESPONSE" | grep -q "success.*true"; then
    USER_ID=$(echo "$USER_RESPONSE" | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)
    print_success "Test user created: $USER_ID"
    
    # Create a test scrape job
    JOB_RESPONSE=$(curl -s -X POST http://localhost:5000/api/queue/scrape \
        -H "Content-Type: application/json" \
        -d "{\"url\": \"https://example.com\", \"userId\": \"$USER_ID\", \"options\": {\"screenshot\": false}}" 2>/dev/null)
    
    if echo "$JOB_RESPONSE" | grep -q "success.*true"; then
        print_success "Test scrape job created successfully"
        JOB_QUEUE_STATUS=0
    else
        print_error "Failed to create test scrape job"
        print_error "Response: $JOB_RESPONSE"
        JOB_QUEUE_STATUS=1
    fi
else
    print_error "Failed to create test user"
    print_error "Response: $USER_RESPONSE"
    JOB_QUEUE_STATUS=1
fi
echo ""

# Test queue statistics
print_status "Testing queue statistics..."
QUEUE_STATS=$(curl -s http://localhost:5000/api/queue/stats 2>/dev/null)
if echo "$QUEUE_STATS" | grep -q "success.*true"; then
    print_success "Queue statistics endpoint working"
else
    print_warning "Queue statistics endpoint not responding properly"
fi
echo ""

# Show container status
print_status "Container status:"
docker-compose -f docker-compose.dev.yml ps
echo ""

# Show logs summary
print_status "Recent logs summary:"
docker-compose -f docker-compose.dev.yml logs --tail=5
echo ""

# Summary
echo "🏁 Test Summary"
echo "==============="

if [ $API_STATUS -eq 0 ]; then
    print_success "✅ API Server: PASSED"
else
    print_error "❌ API Server: FAILED"
fi

if [ $SCRAPER_STATUS -eq 0 ]; then
    print_success "✅ Scraper Engine: PASSED"
else
    print_error "❌ Scraper Engine: FAILED"
fi

if [ -z "$REDIS_FAILED" ]; then
    print_success "✅ Redis: PASSED"
else
    print_error "❌ Redis: FAILED"
fi

if [ $JOB_QUEUE_STATUS -eq 0 ]; then
    print_success "✅ Job Queue Integration: PASSED"
else
    print_error "❌ Job Queue Integration: FAILED"
fi

echo ""

# Overall result
if [ $API_STATUS -eq 0 ] && [ $SCRAPER_STATUS -eq 0 ] && [ -z "$REDIS_FAILED" ] && [ $JOB_QUEUE_STATUS -eq 0 ]; then
    print_success "🎉 All tests PASSED! Docker integration is working correctly."
    echo ""
    echo "Services are running at:"
    echo "  • API Server: http://localhost:5000"
    echo "  • Scraper Engine: http://localhost:5001"
    echo "  • Redis Commander: http://localhost:8081"
    echo ""
    echo "To stop services: make dev-down"
    exit 0
else
    print_error "❌ Some tests FAILED. Please check the logs above."
    echo ""
    echo "To view logs: make dev-logs"
    echo "To stop services: make dev-down"
    exit 1
fi
