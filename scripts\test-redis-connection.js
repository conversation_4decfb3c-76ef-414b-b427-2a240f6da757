#!/usr/bin/env node

/**
 * Test Redis Connection for BullMQ
 * Verifies Redis connection and BullMQ compatibility
 */

const { Queue } = require('bullmq');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

async function testRedisConnection() {
  log.title('🔴 Redis Connection Test for BullMQ');
  log.title('===================================');
  console.log();

  // Test Redis connection with BullMQ-optimized settings
  const redisConfig = {
    host: 'localhost',
    port: 6379,
    maxRetriesPerRequest: null, // BullMQ requirement
    enableOfflineQueue: false,  // Better for BullMQ
    connectTimeout: 10000,
    lazyConnect: true,
  };

  log.info('Testing Redis connection with BullMQ-optimized settings...');
  console.log('Redis Config:', JSON.stringify(redisConfig, null, 2));
  console.log();

  let testQueue;
  try {
    // Create a test queue
    testQueue = new Queue('test-connection', {
      connection: redisConfig,
    });

    // Test connection
    await testQueue.add('test-job', { message: 'Hello Redis!' });
    log.success('✅ Successfully created test job in Redis');

    // Get queue stats
    const waiting = await testQueue.getWaiting();
    const active = await testQueue.getActive();
    const completed = await testQueue.getCompleted();
    const failed = await testQueue.getFailed();

    log.success('✅ Successfully retrieved queue statistics');
    console.log(`  • Waiting jobs: ${waiting.length}`);
    console.log(`  • Active jobs: ${active.length}`);
    console.log(`  • Completed jobs: ${completed.length}`);
    console.log(`  • Failed jobs: ${failed.length}`);

    // Clean up test job
    await testQueue.obliterate({ force: true });
    log.success('✅ Cleaned up test queue');

    console.log();
    log.success('🎉 Redis connection test PASSED!');
    log.info('BullMQ warnings should be eliminated with these settings.');

  } catch (error) {
    log.error('❌ Redis connection test FAILED!');
    log.error(`Error: ${error.message}`);
    console.log();
    log.info('Troubleshooting tips:');
    log.info('1. Ensure Redis is running: npm run redis:start');
    log.info('2. Check Redis container: docker ps | grep redis');
    log.info('3. Test Redis directly: docker exec web-analyzer-redis-dev redis-cli ping');
    process.exit(1);
  } finally {
    if (testQueue) {
      await testQueue.close();
    }
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\nTest interrupted by user.');
  process.exit(1);
});

// Run test
testRedisConnection().catch(error => {
  log.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
