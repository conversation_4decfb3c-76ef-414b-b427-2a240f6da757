@echo off
title Web Analyzer Platform - Quick Start

echo.
echo 🚀 Web Analyzer Platform - Quick Start
echo ======================================
echo.

REM Check if Node.js is installed
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is available
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available!
    echo Please ensure Node.js is properly installed.
    pause
    exit /b 1
)

echo [INFO] Starting Web Analyzer Platform...
echo.

REM Run the Node.js start script
node start.js

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Failed to start the platform!
    echo Try running: npm run setup
    echo.
    pause
    exit /b 1
)

echo.
echo Platform is running! Press any key to exit...
pause >nul
