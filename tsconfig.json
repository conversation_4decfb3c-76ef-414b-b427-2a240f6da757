{"compilerOptions": {"baseUrl": ".", "paths": {"@web-analyzer/*": ["packages/*"], "@web-analyzer/db-models/*": ["packages/db-models/*"], "@web-analyzer/shared-logic/*": ["packages/shared-logic/*"], "@web-analyzer/config/*": ["packages/config/*"], "@web-analyzer/types/*": ["packages/types/*"], "@web-analyzer/utils/*": ["packages/utils/*"], "@web-analyzer/ai-core/*": ["packages/ai-core/*"], "@web-analyzer/audit-engine/*": ["packages/audit-engine/*"], "@web-analyzer/scraper-core/*": ["packages/scraper-core/*"], "@web-analyzer/auth-lib/*": ["packages/auth-lib/*"], "@web-analyzer/report-generator/*": ["packages/report-generator/*"]}, "module": "commonjs", "target": "es2020", "moduleResolution": "node", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "composite": true, "declaration": true}, "exclude": ["node_modules", "dist"], "references": [{"path": "./packages/config"}, {"path": "./packages/db-models"}, {"path": "./packages/shared-logic"}, {"path": "./apps/api-server"}, {"path": "./apps/scraper-engine"}]}